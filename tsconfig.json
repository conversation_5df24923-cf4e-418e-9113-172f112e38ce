{
  "compilerOptions": {
    "esModuleInterop": true,
    "module": "commonjs",
    "target": "ES2020",
    "outDir": "out",
    "lib": ["ES2020", "dom"],
    "sourceMap": true,
    "rootDir": "src",
    "strict": true,
    "types": ["node", "react"],
    "resolveJsonModule": true,
    "noEmit": true
    
    // "paths": {
    //   "@vue/compiler-sfc": [
    //     "./node_modules/@vue/compiler-sfc/dist/compiler-sfc.esm-browser.js",
    //     "./node_modules/@vue/compiler-sfc"
    //   ]
    // },
  },
  "include": ["src/"],
  "exclude": ["node_modules", ".vscode-test", "webview-ui", "setting-ui"],
  "references": [
    {
      "path": "./packages/shared"
    }
  ]
}
