import { InitialConfigType, InitialEditorStateType, LexicalComposer } from "@lexical/react/LexicalComposer";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { EditorRefPlugin } from "@lexical/react/LexicalEditorRefPlugin";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary";
import { useColorMode, useControllableState, useMergeRefs } from "@chakra-ui/react";
import { FocusPlugin } from "@/components/TextArea/lexical/FocusPlugin";
import { SlashPlugin } from "@/components/TextArea/lexical/SlashPlugin";
import {
  MutableRefObject,
  RefCallback,
  RefObject,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import {
  DisableRichEditorMenu,
  SharpCommand,
} from "@shared/types";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import { RichEditorDisabledReason } from "@shared/constant";
import {
  MentionNode,
} from "@/components/TextArea/lexical/CommandNode";
import { DeletePlugin } from "@/components/TextArea/lexical/DeletePlugin";
import { EmptyNode } from "@/components/TextArea/lexical/EmptyNode";
import { SaveStatePlugin } from "@/components/TextArea/lexical/SaveStatePlugin";
import {
  COMMAND_PRIORITY_LOW,
  KEY_ESCAPE_COMMAND,
  LexicalEditor,
  SerializedEditorState,
  SerializedLexicalNode,
} from "lexical";

import { ClearPlugin } from "@/components/TextArea/lexical/ClearPlugin";
import { InsertLineBreakPlugin } from "@/components/TextArea/lexical/InsertLineBreakPlugin";
import { InsertCommandSign } from "@/components/TextArea/lexical/InsertCommandSign";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { EnterSendPlugin } from "@/components/TextArea/lexical/EnterSendPlugin";
import { CommandPluginRef } from "@/components/TextArea";
import { OptPlugin } from "@/components/TextArea/lexical/OptPlugin";
import { DialogSetting } from "@/store/record";
import eventBus from "@/utils/eventBus";
import repoChatService, { WorkspaceState } from "@/services/repo-chat";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { RichEditorContext } from "@/components/TextArea/hooks/useRichEditorContext";
import { twMerge } from "tailwind-merge";
import { CustomVariableNode } from "@/components/TextArea/lexical/CustomVariableNode";
import { CustomVariableAutoUpdatePlugin } from "@/components/TextArea/lexical/CustomVariableAutoUpdatePlugin";
import { MentionNodeV2 } from "@/components/TextArea/lexical/MentionNodeV2/MentionNodeV2";
import { withProviders } from "@udecode/cn";
import { MentionsV2Plugin } from "@/components/TextArea/lexical/MentionsV2Plugin";

interface IProps {
  editorRef?: RefObject<LexicalEditor> | RefCallback<LexicalEditor>;
  panelRef: RefObject<HTMLDivElement>;
  optRef?: RefObject<HTMLDivElement>;
  moreOpt?: React.ReactNode;
  clearRef?: MutableRefObject<{ clear: () => void } | undefined>;
  insertSignRef?: MutableRefObject<
    { insertSign: (sign: string) => void } | undefined
  >;
  onSubmit: () => unknown;
  onStop?: () => unknown;
  onEscape?: () => unknown;
  changeEditorState: (
    state: SerializedEditorState<SerializedLexicalNode>
  ) => void;
  commandShown?: {
    sharpShown: boolean;
    slashShown: boolean;
  };
  onCommandShownChange?: (state: {
    sharpShown: boolean;
    slashShown: boolean;
  }) => void;
  slashPluginRef?: MutableRefObject<CommandPluginRef | undefined>;
  setting?: DialogSetting;
  disabled: boolean;
  className?: string;
  editorClassName?: string;
  customOptions?: {
    slashCommandEnabled?: boolean;
    sharpCommandEnabled?: boolean;
    uploadFileEnabled?: boolean;
    /** 过滤掉知识命令中某些menu的key */
    filterSharpCommandKeyList?: SharpCommand[];
  };
  focused?: boolean;
  onFocusedChange?: (focused: boolean) => void;
  initialEditorState?: InitialEditorStateType;
  editable?: boolean;
  namespace?: string;
  loading?: boolean;
}

export const RichEditor = withProviders()((props: IProps) => {
  const {
    editorRef: editorRefProp,
    customOptions,
    slashPluginRef: slashPluginRefProp,
    onCommandShownChange,
    commandShown: commandShownProp,
    panelRef,
    changeEditorState,
    clearRef,
    onSubmit,
    onStop,
    insertSignRef: insertSignRefProp,
    optRef,
    moreOpt,
    setting,
    disabled,
    initialEditorState,
    className,
    editorClassName,
    editable = true,
    namespace = "RichEditor",
    loading = false,
    onEscape: onEscapeProp,
  } = props;

  const insertSignRefInner = useRef<{ insertSign: (sign: string) => void }>();

  const insertSignRef = useMergeRefs(insertSignRefProp, insertSignRefInner);

  const slashPluginRef = useRef<CommandPluginRef | undefined>();
  const composedSlashPluginRef = useMergeRefs(slashPluginRefProp, slashPluginRef);

  const sharpPluginRef = useRef<CommandPluginRef | undefined>();

  const richEditorPanelMenuStore = useRichEditPanelMenuStore();
  const setDisabledMenu = useRichEditPanelMenuStore(state => state.setDisabledMenu);
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";
  const editorRef = useRef<LexicalEditor | null>(null);
  const composedEditorRef = useMergeRefs(editorRefProp, editorRef);

  const [focused, setFocused] = useControllableState({
    value: props.focused,
    onChange: props.onFocusedChange,
    defaultValue: false,
  });

  const initialConfig = useMemo<InitialConfigType>(() => ({
    namespace: namespace,
    nodes: [MentionNode, EmptyNode, CustomVariableNode, MentionNodeV2],
    onError: (error: any) => {
      throw error;
    },
    editorState: initialEditorState,
    editable,
  }), [editable, initialEditorState, namespace]);

  const setWorkspaceTree = useCallback(
    (files: string[], dirs: string[]) => {
      richEditorPanelMenuStore.setCodeSearchWorkspaceDir(dirs);
      richEditorPanelMenuStore.setCodeSearchWorkspaceFiles(files);
    },
    [richEditorPanelMenuStore],
  );

  const fetchWorkspaceTree = useCallback(async () => {
    const files = await repoChatService.getWorkspaceFileList({
      excludeDirList: ["node_modules", ".kwaipilot/rules"],
    });
    const dir = await repoChatService.getWorkspaceDirList();
    setWorkspaceTree(files, dir);
  }, [setWorkspaceTree]);

  const fetchCurrentFilAndCodeBaseRepo = useCallback(async () => {
    const { filePath, repoPath }
      = await repoChatService.getCurrentFilePathAndRepoPath();
    richEditorPanelMenuStore.updateCodeBasePath(repoPath || "");
    richEditorPanelMenuStore.updateCurrentFilePath(filePath || "");
    richEditorPanelMenuStore.setDisabledMenu({
      [SharpCommand.CURRENT_FILE]: {
        status: !filePath,
        msg: !filePath ? RichEditorDisabledReason.unopenedFile : "",
      },
    });

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    kwaiPilotBridgeAPI.onActiveTextChange((data) => {
      const {
        document: { relativePath },
      } = data;
      // 更新当前文件
      richEditorPanelMenuStore.updateCurrentFilePath(relativePath || "");
      richEditorPanelMenuStore.setDisabledMenu({
        [SharpCommand.CURRENT_FILE]: {
          status: !relativePath,
          msg: !relativePath ? RichEditorDisabledReason.unopenedFile : "",
        },
      });
      if (relativePath) {
        repoChatService.currentFilePath = relativePath;
        repoChatService.setSelectFileQueue(
          WorkspaceState.CODE_SEARCH_SELECT_OPEN_FILE_HISTORY,
          [relativePath],
        );
        repoChatService.setSelectFileQueue(
          WorkspaceState.CODE_SEARCH_SELECT_OPEN_DIR_HISTORY,
          [repoChatService.handleFilePathToDir(relativePath)],
        );
      }
    });
  }, [richEditorPanelMenuStore]);

  const closeSharpPanel = useCallback(() => {
    sharpPluginRef.current?.close();
  }, [sharpPluginRef]);

  useEffect(() => {
    fetchWorkspaceTree();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    fetchCurrentFilAndCodeBaseRepo();
  }, [fetchCurrentFilAndCodeBaseRepo]);

  useEffect(() => {
    const disableMenuListener = (data: DisableRichEditorMenu) => {
      richEditorPanelMenuStore.setDisabledMenu(data);
    };

    eventBus.on("pushRichEditorPanelDisableMenu", disableMenuListener);

    return () => {
      eventBus.off("pushRichEditorPanelDisableMenu", disableMenuListener);
    };
  }, [richEditorPanelMenuStore]);

  useEffect(() => {
    kwaiPilotBridgeAPI.getSystemInfo().then((data) => {
      if (data.ide !== "kwaipilot-xcode") {
        setDisabledMenu({
          [SharpCommand.FILE]: {
            status: false,
            msg: "",
          },
        });
      }
    });
  }, [setDisabledMenu]);

  const [commandShown, setCommandShown] = useControllableState({
    value: commandShownProp,
    onChange: onCommandShownChange,
    defaultValue: {
      sharpShown: false,
      slashShown: false,
    },
  });

  const [mentionShown, setMentionShown] = useState(false);

  useEffect(() => {
    return editorRef.current?.registerCommand(KEY_ESCAPE_COMMAND, () => {
      onEscapeProp?.();
      return false;
    }, COMMAND_PRIORITY_LOW);
  }, [onEscapeProp]);

  return (
    <div
      className={className}
    >
      <RichEditorContext.Provider value={{ focused, setFocused, commandShown, setCommandShown, mentionShown, setMentionShown }}>
        <LexicalComposer initialConfig={initialConfig}>
          <RichTextPlugin
            contentEditable={(
              <ContentEditable
                className={twMerge(
                  "w-full text-[13px] leading-[20px] pl-3 pr-2 pt-2 z-10 cursor-text relative focus-visible:outline-none",
                  [isDark ? "text-white" : "text-[#262A2F]"],
                  editorClassName,
                )}
                style={{
                  outline: "none",
                  fontFamily: "PingFang SC, PingFang SC-Regular",
                }}
              >
              </ContentEditable>
            )}
            placeholder={(
              <div
                className="absolute text-[13px] leading-[19.5px] text-text-common-tertiary top-2 left-3"
              >
                有问题尽管问我，# 引用知识
              </div>
            )}
            ErrorBoundary={LexicalErrorBoundary}
          />
          <EditorRefPlugin editorRef={composedEditorRef} />
          <HistoryPlugin />
          <AutoFocusPlugin />
          <FocusPlugin changeFocused={setFocused} />
          {(customOptions?.slashCommandEnabled ?? true) && (
            <SlashPlugin
              panelRef={panelRef}
              ref={composedSlashPluginRef}
              closeSharpPanel={closeSharpPanel}
            />
          )}
          {(customOptions?.sharpCommandEnabled ?? true) && (
            <MentionsV2Plugin />
          )}
          <EnterSendPlugin submit={onSubmit} />
          <DeletePlugin />
          <SaveStatePlugin changeEditorState={changeEditorState} />
          <ClearPlugin ref={clearRef} />
          <InsertLineBreakPlugin />
          <InsertCommandSign focused={focused} ref={insertSignRef} />
          {optRef && (
            <OptPlugin
              loading={loading}
              optRef={optRef}
              moreOpt={moreOpt}
              setting={setting}
              onSubmit={onSubmit}
              onStop={onStop}
              insertSignRef={insertSignRefInner}
              disabled={disabled}
              sharpPluginRef={sharpPluginRef}
              slashPluginRef={slashPluginRef}
              focused={focused}
              slashCommandEnabled={customOptions?.slashCommandEnabled}
              sharpCommandEnabled={false}
              uploadFileEnabled={customOptions?.uploadFileEnabled}
            >
            </OptPlugin>
          )}
          <CustomVariableAutoUpdatePlugin />
        </LexicalComposer>
      </RichEditorContext.Provider>
    </div>
  );
});
