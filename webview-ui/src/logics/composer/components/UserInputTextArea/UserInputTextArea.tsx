import { useCallback, useState, useMemo, useRef, createContext, useContext, useEffect } from "react";
import { useBoolean, useColorMode, useMergeRefs } from "@chakra-ui/react";
import clsx from "clsx";

import {
  SharpCommand,
  // RichEditorBoxPanelData,
} from "@shared/types";
import { RichEditor } from "./RichEditor";
import { LexicalEditor, SerializedEditorState, SerializedLexicalNode } from "lexical";
import css from "./UserInputeTextArea.module.less";
import { Enable, Resizable } from "re-resizable";
import { kwaiPilotBridgeAPI } from "@/bridge";
import ApplyStatus from "../ApplyStatus";
import { useComposerState } from "../../context/ComposerStateContext";
import { isMentionNode } from "shared/lib/MentionNode";
import { ModelSelector } from "../ModelSelector";
import { InternalLocalMessage, isHumanMessage } from "shared/lib/agent";
import { CustomScrollBar } from "@/components/CustomScrollbar";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { ContextHeader } from "./ContextHeader";
import { ContextHeaderContext, useContextHeaderState } from "./ContextHeader/ContextHeaderContext";
import { useSubmit } from "./useSubmit";
import { useEvent } from "react-use";
import { McpStatusBar } from "../Mcp/McpStatusBar";
import { isMentionNodeV2 } from "shared/lib/MentionNodeV2/nodes";
import { isCustomVariableNode } from "shared/lib/CustomVariable";
import { useContextInitiation } from "./useContextInitiation";
import { EventBusEvents, useEventBusListener } from "@/utils/eventBus";

export interface UploadFile {
  biz?: string;
  filename: string;
  id?: number;
  uid: string;
  path?: string;
  url?: string;
  type?: string;
  size: number;
  username?: string;
  progress?: number;
  status?: "uploading" | "done" | "error";
  [key: string]: any;
}
export interface DocListType {
  id: number;
  name: string;
  urls?: string[];
}

export interface CommandPluginRef {
  close: () => void;
  open: () => void;
  getShown: () => boolean;
}

interface IProps {
  editorClassName?: string;
  wrapperClassName?: string;
  enable?: Enable | false;
  hiddenApplyStatus?: boolean;
  /* 如果是正在编辑的某条消息 */
  localMessage?: InternalLocalMessage;
  role: "bottom" | "conversation";
  editorRef?: React.RefObject<LexicalEditor>;
}

export const UserInputTextAreaContext = createContext<{
  role: IProps["role"];
} | null>(null);

export function useUserInputTextAreaContext() {
  const context = useContext(UserInputTextAreaContext);
  if (!context) {
    throw new Error("UserInputTextAreaContext is not found");
  }
  return context;
}

export const UserInputTextArea: React.FC<IProps> = (props: IProps) => {
  const {
    editorClassName,
    wrapperClassName,
    hiddenApplyStatus = false,
    localMessage,
    role,
    editorRef: editorRefProp,
  } = props;
  if (localMessage && !isHumanMessage(localMessage)) {
    throw new Error("data is not a human message");
  }

  const { colorMode: theme } = useColorMode();
  const { isStreaming, sessionId, editingMessageTs } = useComposerState();

  const contextHeaderState = useContextHeaderState({
    initialNodes: localMessage?.contextItems?.map(v => ({
      structure: v,
      followActiveEditor: false,
      isVirtualContext: false,
    })) || [],
  });

  const initialEditorState = useMemo(() => localMessage?.editorState ? JSON.stringify(localMessage.editorState) : undefined, [localMessage]);

  const optRef = useRef<HTMLDivElement>(null);
  const slashPluginRef = useRef<CommandPluginRef>();

  const editorRef = useRef<LexicalEditor>(null);
  const composedEditorRef = useMergeRefs(editorRef, editorRefProp);

  const clearRef = useRef<{
    clear: () => void;
  }>();
  const [richEditorState, setRichEditorState]
    = useState<SerializedEditorState<SerializedLexicalNode>>();
  const changeEditorState = useCallback(
    (state: SerializedEditorState<SerializedLexicalNode>) => {
      setRichEditorState(state);
    },
    [setRichEditorState],
  );

  const {
    resetContext,
  } = useContextInitiation({
    /* 如果有正在编辑的历史消息, 优先使用历史消息 */
    isContextConsumer: role === "bottom" ? !editingMessageTs : true,
    contextHeaderState,
  });

  const resetEditorState = useCallback(() => {
    if (role !== "bottom") {
      /* 历史消息不绑定 */
      return;
    }
    resetContext();
    clearRef.current?.clear();
  }, [resetContext, role]);

  useEffect(() => {
    resetEditorState();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sessionId]);

  const isDark = theme === "dark";

  const [focused, setFocused] = useState(false);
  /** 进入子菜单要标识进入的是哪个子菜单 */
  const panelRef = useRef<HTMLDivElement>(null);

  const onResetCheckpoint: EventBusEvents["composer:onResetCheckpoint"] = useCallback(({ humanMessage }) => {
    const editor = editorRef.current;
    if (!editor) {
      return;
    }
    editor.setEditorState(editor.parseEditorState(humanMessage.editorState));
    if (humanMessage.contextItems.length) {
      contextHeaderState.setNodes(humanMessage.contextItems.map(v => ({
        structure: v,
        followActiveEditor: false,
        isVirtualContext: false,
      })));
    }
  }, [contextHeaderState]);

  useEventBusListener("composer:onResetCheckpoint", onResetCheckpoint);

  const isBlank = useMemo(() => {
    function walkHasText(node: SerializedLexicalNode): boolean {
      if (isCustomVariableNode(node)) {
        return true;
      }
      if (isMentionNode(node)) {
        return true;
      }
      else if (isMentionNodeV2(node)) {
        return true;
      }

      else if ("text" in node) {
        return Boolean(String(node.text).trim());
      }
      if ("children" in node && Array.isArray(node.children)) {
        for (const child of node.children) {
          if (walkHasText(child)) {
            return true;
          }
        }
      }
      return false;
    }
    return richEditorState ? !walkHasText(richEditorState.root) : true;
  }, [richEditorState]);

  const disabled = !isStreaming && isBlank;

  const stopCurrentTask = useCallback(() => {
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "stop",
      params: {
        sessionId,
      },
    });
  }, [sessionId]);
  const onReadonlyMaskClick = useCallback(() => {
    if (role === "bottom" && editingMessageTs) {
      kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(undefined);
    }
  }, [editingMessageTs, role]);

  const isDeveloperMode = useBridgeObservableAPI("isDeveloperMode");

  const moreOpt = (
    <>
      <McpStatusBar />
      {isDeveloperMode ? <ModelSelector /> : null}
    </>
  );

  const onEscape = useCallback(() => {
    if (localMessage) {
      kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(undefined);
    }
  }, [localMessage]);
  const { doSubmit: _doSubmit } = useSubmit({
    isBlank,
    editor: editorRef.current,
    contextHeaderState,
    role,
  });

  const doSubmit = useCallback(async () => {
    const res = await _doSubmit();
    if (res.result) {
    /* 完成回答后清空 https://team.corp.kuaishou.com/task/B2489947 */
      resetEditorState();
    }
  }, [_doSubmit, resetEditorState]);

  const dragRef = useRef<HTMLDivElement>(null);
  const { isDragging } = useDragFile(dragRef);
  const [, { on: onResize }] = useBoolean();

  const resizableRef = useRef<Resizable>(null);
  useEffect(() => {
    if (!props.enable) {
      // 如果关闭 resize 需要把 reziable 重新设置尺寸
      resizableRef.current?.updateSize({ });
    }
  });

  return (
    <ContextHeaderContext.Provider value={{ state: contextHeaderState }}>
      <UserInputTextAreaContext.Provider value={{ role }}>
        <div
          className={clsx(
            "flex relative flex-col",
            wrapperClassName,
          )}
          onClick={onReadonlyMaskClick}
        >
          <div>
            {hiddenApplyStatus ? null : <ApplyStatus />}
            <Resizable
              ref={resizableRef}
              enable={props.enable ?? false}
              maxHeight={340}
              minHeight={116}
              onResize={onResize}
            >
              <ColorfulBorder className=" h-full" focused={focused} disableColorfulBorder={role !== "bottom"}>
                <div
                  className="bg-bg-input-fill flex flex-col rounded-lg transition-all relative h-full z-10 p-[1px]"
                  style={{
                    maxHeight: "340px",
                    boxShadow: !focused
                      ? ""
                      : isDark
                        ? "box-shadow: 0px 16px 30px 0px rgba(30, 147, 252, 0.08), 0px 8px 21.6px 0px rgba(36, 143, 253, 0.12), 0px 4px 4px 0px rgba(26, 150, 251, 0.08), 0px 2px 2px 0px rgba(24, 151, 251, 0.06)"
                        : "box-shadow: 0px 16px 30px 0px rgba(138, 192, 255, 0.08), 0px 8px 21.6px 0px rgba(138, 192, 255, 0.08), 0px 4px 4px 0px rgba(138, 192, 255, 0.08), 0px 2px 2px 0px rgba(138, 192, 255, 0.06)",
                  }}
                  ref={dragRef}
                >
                  <div ref={panelRef}></div>
                  {editorRef.current && <ContextHeader zIndex={1} editor={editorRef.current} disabledCurrentFileBinding={!!localMessage} />}
                  <CustomScrollBar className=" flex-auto">
                    <RichEditor
                      editorRef={composedEditorRef}
                      customOptions={{
                        slashCommandEnabled: false,
                        sharpCommandEnabled: true,
                        uploadFileEnabled: false,
                        filterSharpCommandKeyList: [SharpCommand.CODEBASE, SharpCommand.FOLDER],
                      }}
                      initialEditorState={initialEditorState}
                      onSubmit={doSubmit}
                      onStop={stopCurrentTask}
                      onEscape={onEscape}
                      panelRef={panelRef}
                      optRef={optRef}
                      moreOpt={moreOpt}
                      changeEditorState={changeEditorState}
                      clearRef={clearRef}
                      slashPluginRef={slashPluginRef}
                      disabled={disabled}
                      editorClassName={editorClassName}
                      className={clsx(
                        "flex-auto relative bg-bg-input-fill ",
                        {
                          "rounded-t-[7.5px] ": true,
                        },
                      )}
                      focused={focused}
                      onFocusedChange={setFocused}
                      loading={isStreaming}
                    />
                  </CustomScrollBar>
                  <div ref={optRef} className="bg-bg-input-fill rounded-b-[7px]"></div>
                </div>

                {isDragging && <div className=" absolute left-0 right-0 top-0 bottom-0 bg-[hsla(216,_28%,_14%,_0.7)] rounded-lg z-10"></div>}
              </ColorfulBorder>
            </Resizable>
          </div>
        </div>
      </UserInputTextAreaContext.Provider>
    </ContextHeaderContext.Provider>
  );
};

function ColorfulBorder({ focused, children, disableColorfulBorder, className }: {
  focused: boolean;
  children: React.ReactNode;
  disableColorfulBorder?: boolean;
  className?: string;
}) {
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";
  return (
    <div className={clsx(
      " relative p-[1px]",
      css.colorfulBorder,
      focused && !disableColorfulBorder
        ? css.colorfulBorderFocused
        : isDark
          ? css.colorfulBorderDark
          : css.colorfulBorderLight,
      className,
    )}
    >
      {children}
    </div>
  );
}

/**
 * TODO: 实现有问题
 * @param ref
 * @returns
 */
function useDragFile(ref: React.RefObject<HTMLElement>) {
  const [isDragging, setIsDragging] = useState(false);

  useEvent("dragover", (e: Event) => {
    const dragEvent = e as DragEvent;
    dragEvent.preventDefault();
    dragEvent.stopPropagation();
    setIsDragging(true);
  }, ref.current);

  useEvent("dragleave", (e: Event) => {
    const dragEvent = e as DragEvent;
    dragEvent.preventDefault();
    dragEvent.stopPropagation();
    setIsDragging(false);
  }, ref.current);

  useEvent("drop", (e: Event) => {
    const dragEvent = e as DragEvent;
    dragEvent.preventDefault();
    dragEvent.stopPropagation();
    setIsDragging(false);

    if (dragEvent.dataTransfer) {
      const items = dragEvent.dataTransfer.items;
      const files = dragEvent.dataTransfer.files;

      console.log("拖拽的文件列表:", Array.from(files));

      // 遍历拖拽的项目
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.kind === "file") {
          const file = item.getAsFile();
          if (file) {
            console.log("文件信息:", {
              name: file.name,
              type: file.type,
              size: file.size,
              lastModified: file.lastModified,
            });
          }
        }
      }
    }
  }, ref.current);

  return {
    isDragging,
  };
}
