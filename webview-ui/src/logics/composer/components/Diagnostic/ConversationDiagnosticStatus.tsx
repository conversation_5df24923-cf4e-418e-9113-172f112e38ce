import { Flex, Box } from "@chakra-ui/react";
import IconArrow from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_smallarrow_right";
import KidIcon from "@kid/enterprise-icon/icon/output/Icon";
import { useBoolean } from "react-use";
import { CustomScrollBar } from "@/components/CustomScrollbar";
import AutoTooltip from "@/components/AutoTooltip";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { DiagnosticSeverity, SerializedDiagnostic } from "shared/lib/misc/diagnostic";
import { useDesignToken } from "@/hooks/useDesignToken";
import React, { useCallback, useEffect, useMemo } from "react";
import { useComposerState } from "../../context/ComposerStateContext";
import { useComposerConversationContext } from "../../context/ComposerConversationContext";
import { generateCustomUUID } from "@/utils/sessionUtils";
import { SerializedEditorState, RootNode, ParagraphNode, LineBreakNode, SerializedLineBreakNode, SerializedLexicalNode, TextNode, SerializedTextNode } from "lexical";
import { transformToPlainTextForHumanReading } from "@/components/TextArea/lexical/editorState";
import { isHumanMessage } from "shared/lib/agent";
import { ReportOpt } from "@shared/types/logger";
import { reportUserAction } from "@/utils/weblogger";
import IconSkip from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_skip";
import { vsCss } from "@/style/vscode";

function IconAlert({ className }: { className?: string }) {
  return (
    <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg" className={className}>
      <path d="M13.1392 10.5576L8.35678 2.25219C8.21826 2.01529 8.02013 1.81878 7.78209 1.68223C7.54405 1.54567 7.27441 1.47382 6.99999 1.47382C6.72556 1.47382 6.45592 1.54567 6.21788 1.68223C5.97984 1.81878 5.78171 2.01529 5.64319 2.25219L0.860767 10.5576C0.726356 10.7875 0.655518 11.049 0.655518 11.3153C0.655518 11.5816 0.726356 11.8431 0.860767 12.073C0.997753 12.311 1.19561 12.5084 1.43405 12.6448C1.67248 12.7811 1.9429 12.8516 2.21756 12.849H11.7824C12.0569 12.8514 12.3271 12.7809 12.5653 12.6445C12.8035 12.5081 13.0012 12.3109 13.1381 12.073C13.2727 11.8432 13.3437 11.5817 13.3439 11.3154C13.3441 11.0491 13.2734 10.7876 13.1392 10.5576ZM12.0017 11.4162C11.9795 11.4541 11.9474 11.4853 11.9089 11.5064C11.8704 11.5276 11.8269 11.538 11.783 11.5365H2.21756C2.17365 11.538 2.13013 11.5276 2.09163 11.5064C2.05312 11.4853 2.02106 11.4541 1.99881 11.4162C1.97995 11.3855 1.96996 11.3502 1.96996 11.3142C1.96996 11.2782 1.97995 11.2429 1.99881 11.2122L6.78124 2.9068C6.80492 2.87034 6.83733 2.84039 6.87554 2.81965C6.91374 2.79891 6.95652 2.78805 6.99999 2.78805C7.04345 2.78805 7.08623 2.79891 7.12444 2.81965C7.16264 2.84039 7.19506 2.87034 7.21874 2.9068L12.0006 11.2122C12.0196 11.2428 12.0298 11.278 12.03 11.314C12.0302 11.35 12.0204 11.3854 12.0017 11.4162ZM6.34374 7.81773V6.06773C6.34374 5.89368 6.41288 5.72676 6.53595 5.60369C6.65902 5.48062 6.82594 5.41148 6.99999 5.41148C7.17403 5.41148 7.34095 5.48062 7.46402 5.60369C7.5871 5.72676 7.65624 5.89368 7.65624 6.06773V7.81773C7.65624 7.99178 7.5871 8.1587 7.46402 8.28177C7.34095 8.40484 7.17403 8.47398 6.99999 8.47398C6.82594 8.47398 6.65902 8.40484 6.53595 8.28177C6.41288 8.1587 6.34374 7.99178 6.34374 7.81773ZM7.87499 10.0052C7.87499 10.1783 7.82367 10.3475 7.72752 10.4914C7.63138 10.6353 7.49472 10.7474 7.33483 10.8136C7.17495 10.8799 6.99902 10.8972 6.82928 10.8634C6.65955 10.8297 6.50364 10.7463 6.38127 10.624C6.2589 10.5016 6.17556 10.3457 6.1418 10.1759C6.10804 10.0062 6.12536 9.83027 6.19159 9.67038C6.25782 9.5105 6.36997 9.37384 6.51386 9.2777C6.65775 9.18155 6.82693 9.13023 6.99999 9.13023C7.23205 9.13023 7.45461 9.22242 7.6187 9.38651C7.7828 9.55061 7.87499 9.77317 7.87499 10.0052Z" fill="currentColor" />
    </svg>
  );
}

function diagnosticsToProblemsString(
  diagnostics: [string, SerializedDiagnostic[]][],
  severities: DiagnosticSeverity[],
): string {
  let result = "";
  for (const [uri, fileDiagnostics] of diagnostics) {
    const problems = fileDiagnostics.filter(d => severities.includes(d.severity));
    if (problems.length > 0) {
      result += `\n\n${uri}`;
      for (const diagnostic of problems) {
        let label: string;
        switch (diagnostic.severity) {
          case DiagnosticSeverity.Error:
            label = "Error";
            break;
          case DiagnosticSeverity.Warning:
            label = "Warning";
            break;
          case DiagnosticSeverity.Information:
            label = "Information";
            break;
          case DiagnosticSeverity.Hint:
            label = "Hint";
            break;
          default:
            label = "Diagnostic";
        }
        const line = diagnostic.range.start.line + 1; // VSCode lines are 0-indexed
        const source = diagnostic.source ? `${diagnostic.source} ` : "";
        result += `\n- [${source}${label}] Line ${line}: ${diagnostic.message}`;
      }
    }
  }
  return result.trim();
}

function createLintFixEditorState(diagnostics: [string, SerializedDiagnostic[]][]) {
  const linesText: SerializedTextNode[] = [
    {
      type: TextNode.getType(),
      version: 1,
      text: "修复这些报错",
      detail: 0,
      format: 0,
      mode: "normal",
      style: "",
    },
    {
      type: TextNode.getType(),
      version: 1,
      text: diagnosticsToProblemsString(diagnostics, [DiagnosticSeverity.Error]),
      detail: 0,
      format: 0,
      mode: "normal",
      style: "",
    },
  ];
  const lines: SerializedLexicalNode[] = linesText.flatMap((line, i) => i < linesText.length - 1
    ? [
        line,
         {
           type: LineBreakNode.getType(),
           version: 1,
         } satisfies SerializedLineBreakNode,
      ]
    : line);
  const result: SerializedEditorState = {
    root: {
      version: 1,
      type: RootNode.getType(),
      children: [
        {
          type: ParagraphNode.getType(),
          version: 1,
          children: lines,
        },
      ],
      direction: null,
      indent: 0,
      format: "",
    } as any,
  };
  return result;
}

export function ConversationDiagnosticStatus({
  isLastConversation,
}: {
  isLastConversation: boolean;
}) {
  const { tokens } = useDesignToken();
  const { currentTaskInterrupted, isStreaming, sessionId } = useComposerState();
  const { isFinished, humanMessage } = useComposerConversationContext();

  /**
   * 什么情况展示{修复}按钮?
   *
   * * 最新一轮对话
   * * 对话已经结束
   *
   */
  const showFixButton = useMemo(() => {
    if (!isLastConversation) {
      return false;
    }
    if (currentTaskInterrupted) {
      return false;
    }
    if (!isFinished) {
      return false;
    }
    if (isStreaming && isLastConversation) {
      return false;
    }
    return true;
  }, [currentTaskInterrupted, isFinished, isLastConversation, isStreaming]);

  const isHighlighted = showFixButton;

  const [isExpanded, toggleExpanded] = useBoolean(false);

  useEffect(() => {
    // 出现 fixButton 时, 自动展开
    if (showFixButton) {
      toggleExpanded(true);
    }
    else {
      toggleExpanded(false);
    }
  }, [showFixButton, toggleExpanded]);

  const HIGHLIGHT_COLOR = "hsla(6, 83%, 59%, 1)";

  if (!isHumanMessage(humanMessage)) {
    throw new Error("ConversationDiagnosticStatus: humanMessage is not a human message");
  }
  const diagnostics = useMemo(() => humanMessage.diagnostics || [], [humanMessage.diagnostics]);

  const flattenDiagnostics = (diagnostics || []).flatMap(([uri, diagnostics]) =>
    diagnostics.map(diagnostic => [uri, diagnostic] as [string, SerializedDiagnostic]),
  );

  const handleDiagnosticClick = useCallback((uri: string, diagnostic: SerializedDiagnostic) => {
    kwaiPilotBridgeAPI.extensionComposer.$locateDiagnostic(uri, diagnostic);
  }, []);

  const onFixButtonClick = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();

    const conversationId = generateCustomUUID();

    const editorState = createLintFixEditorState(diagnostics);

    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "newTask",
      task: ""/* 废弃字段 */,
      reqData: {
        sessionId,
        chatId: conversationId,
      },
      rules: [],
      editorState,
      questionForHumanReading: transformToPlainTextForHumanReading(editorState),
      contextItems: [],
      editingMessageTs: undefined,
    });
    toggleExpanded(false);
    const param: ReportOpt<"agent_linter_fix_click"> = {
      key: "agent_linter_fix_click",
      type: "fix_click",
    };
    reportUserAction(param, humanMessage.chatId, humanMessage.sessionId);
  }, [diagnostics, humanMessage.chatId, humanMessage.sessionId, sessionId, toggleExpanded]);

  const onLintSettingButtonClick = useCallback((e: React.MouseEvent<HTMLButtonElement>) => {
    kwaiPilotBridgeAPI.extensionComposer.$openDiagnosticSetting();
    e.stopPropagation();
  }, []);

  /**
   * 什么情况展示 error
   *
   * * 有 error
   */
  if (!flattenDiagnostics.length) {
    return null;
  }

  return (
    <div className="border border-solid border-border-common rounded-lg w-full">
      {/* header */}
      <Flex gap={1} px={3} py={2} onClick={() => toggleExpanded()} align="center" cursor="pointer">
        <Box className={` ${isExpanded ? "rotate-90" : ""}`} color={isHighlighted ? HIGHLIGHT_COLOR : tokens.colorTextCommonPrimary}>
          <KidIcon config={IconArrow} size={14} />
        </Box>
        <Box color={isHighlighted ? HIGHLIGHT_COLOR : tokens.colorTextCommonSecondary}>
          <IconAlert />
        </Box>
        <AutoTooltip placement="top-start" label={`${flattenDiagnostics.length} 处代码错误`} openDelay={700}>
          <Box as="span" color={isHighlighted ? HIGHLIGHT_COLOR : tokens.colorTextCommonPrimary} className="text-[12px] leading-[18px]">

            {flattenDiagnostics.length}
            {" "}
            处代码错误
          </Box>
        </AutoTooltip>
        <Box className=" border border-solid leading-[13px] h-[15px] px-[3px] rounded-[8px]" bg={vsCss.textPreformatBackground} color={vsCss.foreground} borderColor={vsCss.checkboxBorder}>
          <span className="text-[12px] font-medium inline-block scale-[0.83]">
            Beta
          </span>
        </Box>
        <Flex align="center" ml="auto" gap={3}>
          <button onClick={onLintSettingButtonClick} className=" ml-3 float-none flex gap-1 items-center hover:text-text-brand-hover text-text-common-secondary">
            <KidIcon config={IconSkip} size={14} color="inherit" className="flex-none"></KidIcon>
            <span className=" whitespace-nowrap text-[12px] leading-[18px]">
              自动修复设置
            </span>
          </button>
          {showFixButton && (
            <Box
              as="button"
              className="px-[6px] float-none hover:opacity-90 text-[12px] font-medium flex items-center gap-1 text-white leading-[18px] py-[2px] rounded"
              backgroundColor={vsCss.buttonBackground}
              color={vsCss.buttonForeground}
              onClick={onFixButtonClick}
            >
              <span className=" whitespace-nowrap">修复</span>
            </Box>
          )}
        </Flex>
      </Flex>
      {/* body */}
      {isExpanded && (
        <CustomScrollBar className="p-2 max-h-[100px] w-full">
          {flattenDiagnostics.map(([uri, diagnostic], index) => (
            <div
              key={index}
              className="cursor-pointer px-3 h-[22px] box-border w-full flex items-center"
              onClick={() => {
                handleDiagnosticClick(uri, diagnostic);
              }}
            >
              <Box bgColor={isHighlighted ? HIGHLIGHT_COLOR : tokens.colorTextCommonSecondary} className="w-1 h-1 rounded-full mr-2 flex-none"></Box>
              <AutoTooltip label={diagnostic.message} openDelay={700} className="text-text-common-secondary w-0 flex-auto text-[12px] leading-[18px]">
                {diagnostic.message}
              </AutoTooltip>
            </div>
          ))}
        </CustomScrollBar>
      )}
    </div>
  );
}
