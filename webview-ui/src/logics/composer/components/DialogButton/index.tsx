import { Box, HTMLChakraProps, useColorMode } from "@chakra-ui/react";
import clsx from "clsx";
import { forwardRef } from "react";

export interface DialogButtonProps extends HTMLChakraProps<"button"> {
  isPrimary?: boolean;
}

export const DialogButton = forwardRef<HTMLButtonElement, DialogButtonProps>(({ isPrimary = false, children, className, ...props }, ref) => {
  const { colorMode } = useColorMode();
  const isDark = colorMode === "dark";
  return (
    <Box
      as="button"
      {...props}
      ref={ref}
      className={clsx(
        isPrimary
          ? ` bg-text-common-primary ${isDark ? "text-[#353C47]" : "text-[#DEE0E8]"}`
          : "  bg-bg-scrollbar-default  text-text-common-secondary",
        "text-[12px] leading-[18px] font-medium px-[6px] py-[2px] rounded-[4px]",
        className,
      )}
    >
      {children}
    </Box>
  );
},
);
