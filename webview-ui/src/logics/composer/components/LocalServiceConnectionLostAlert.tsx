import AutoTooltip from "@/components/AutoTooltip";
import { Alert, AlertIcon, Box, BoxProps, CloseButton, useDisclosure } from "@chakra-ui/react";

export function LocalServiceConnectionLostAlert(props: BoxProps) {
  const {
    isOpen: isVisible,
    onClose,
  } = useDisclosure({ defaultIsOpen: true });
  if (!isVisible) {
    return null;
  }
  return (
    <Box {...props}>
      <Alert w="full" height="36px" status="error" alignItems="center">
        <AlertIcon boxSize="4" />
        <AutoTooltip label="本地服务连接已丢失，请检查本地服务是否正常运行" className=" text-[12px] font-medium">
          本地服务连接已丢失，请检查本地服务是否正常运行
        </AutoTooltip>
        <CloseButton
          alignSelf="flex-start"
          onClick={onClose}
          ml="auto"
          css={{
            alignSelf: "center",
          }}
        />
      </Alert>
    </Box>
  );
}
