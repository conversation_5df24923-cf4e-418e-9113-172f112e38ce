import { memo } from "react";
import { getHighLight } from "@/components/Dialog/Highlight";
import styles from "./CodeBlockBase.module.less";
import clsx from "clsx";
import { useVsEditorConfig } from "@/store/vsEditorConfig";

/*
overflowX: auto + inner div with padding results in an issue where the top/left/bottom padding renders but the right padding inside does not count as overflow as the width of the element is not exceeded. Once the inner div is outside the boundaries of the parent it counts as overflow.
https://stackoverflow.com/questions/60778406/why-is-padding-right-clipped-with-overflowscroll/77292459#77292459
this fixes the issue of right padding clipped off
"ideal" size in a given axis when given infinite available space--allows the syntax highlighter to grow to largest possible width including its padding
minWidth: "max-content",
*/

interface CodeBlockProps {
  className?: string;
  source?: string;
  forceWrap?: boolean;
  language?: string;
  clearPrebg?: boolean;
  clearPreTopBorder?: boolean;
}

// function StyledMarkdown({ forceWrap, children }: { forceWrap: boolean; children: React.ReactNode }) {
//   return <div className={`markdown-body ${forceWrap ? "forceWrap" : ""}`}>{children}</div>;
// }

// function StyledPre({ children }: { children: React.ReactNode }) {
//   return <pre className="blog-pre">{children}</pre>;
// }

const CodeBlock = memo(({ source, language, forceWrap = false, className, clearPrebg = false, clearPreTopBorder = false }: CodeBlockProps) => {
  const editorConfig = useVsEditorConfig(state => state.editorConfig);
  const html = getHighLight({
    code: source || "", language: language || "typescript", theme: editorConfig.theme, transformers: [
      {
        pre(node) {
          node.properties.style = `background-color: transparent;padding-bottom: 20px;;padding-top: 8px;`;
        },
        code(node) {
          node.properties.style = `background-color: transparent;font-family: ${editorConfig.fontFamily};`;
        },
        line(node) {
          node.properties.style = `padding: 0 12px`;
        },
      },
    ],
  });
  return (
    <div
      style={{
        overflowY: forceWrap ? "visible" : "auto",
        maxHeight: forceWrap ? "none" : "100%",
        borderBottomLeftRadius: "5px",
        borderBottomRightRadius: "5px",
      }}
      className={clsx(styles.markdownBody, className, clearPrebg ? styles.nonePreBg : "", clearPreTopBorder ? styles.nonePreTopBorder : "")}
      dangerouslySetInnerHTML={{ __html: html }}
    />
  );
});

export default CodeBlock;
