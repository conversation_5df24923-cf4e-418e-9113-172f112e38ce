import { kwaiPilotBridgeAPI } from "@/bridge";
import AutoTooltip from "@/components/AutoTooltip";
import { useVsEditorConfig } from "@/store/vsEditorConfig";
import { useCallback } from "react";

function isValidColor(color: string) {
  // 如果输入不是字符串，返回 false
  if (typeof color !== "string") return false;

  // 去除空格
  color = color.trim();

  let div = null;
  // 创建一个临时 div 元素
  div = document.createElement("div");

  // 设置颜色
  div.style.color = color;

  const is = div.style.color !== "";

  div = null;
  // 如果颜色设置无效，会返回空字符串
  return is;
}

export const InlineCode = ({ c }: { c: string }) => {
  c = c.trim();
  const isColorTxt = isValidColor(c || "");

  const config = useVsEditorConfig(state => state.editorConfig);

  const onClick = useCallback(() => {
    navigator.clipboard.writeText(c);
    kwaiPilotBridgeAPI.showToast({
      level: "info",
      message: "已复制",
    });
  }, [c]);

  if (isColorTxt) {
    return (
      <span
        className="kwaipilot-inline-code inline-flex items-center px-1 rounded border-[0.6px] cursor-pointer border-[#4F565F] h-[18px] align-middle  w-fit mx-1 truncate max-w-full whitespace-nowrap hover:bg-send-hover"
        style={{
          fontFamily: config.fontFamily,
        }}
        onClick={onClick}
      >
        <span
          className="size-[12px] rounded-sm mr-1"
          style={{
            background: c,
          }}
        >
        </span>
        <span className="truncate whitespace-nowrap align-middle leading-[18px]">
          {c}
        </span>
      </span>
    );
  }

  return (
    <span
      className="kwaipilot-inline-code inline-flex items-center px-1 rounded  bg-bg-fill h-[18px] align-middle  w-fit mx-1 truncate max-w-full whitespace-nowrap"
      style={{
        fontFamily: config.fontFamily,
      }}
    >
      <AutoTooltip title={c} className="align-middle leading-[18px]">
        {c}
      </AutoTooltip>
    </span>
  );
};
