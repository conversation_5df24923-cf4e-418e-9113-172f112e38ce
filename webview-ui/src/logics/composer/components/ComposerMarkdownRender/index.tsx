import ReactMarkdown, { Components } from "react-markdown";
import { type Root } from "react-markdown/lib/index";
import style from "./index.module.less";
import clsx from "clsx";
import { InlineCode } from "./InlineCode";
import { CodeBlock } from "./CodeBlock";
import { useMemo } from "react";
import RemarkGfm from "remark-gfm";
import { visit, BuildVisitor } from "unist-util-visit";

const addInlineFlagToCode: any = () => {
  const visitor: BuildVisitor<Root> = (tree) => {
    visit(tree, (node, _index, parent) => {
      // 检查节点是否为 code 元素
      if (node.type === "element" && node.tagName === "code") {
        node.properties = node.properties || {};
        node.properties.isInline = !(parent?.type === "element" && parent.tagName === "pre");
      }
    });
  };
  return visitor;
};

interface IProps {
  handleCopy: (text: string) => void;
  content: string;
}

export const ComposerMarkdownRender = ({ content, handleCopy }: IProps) => {
  const components: Components = useMemo(() => {
    return {
      code(props) {
        const { children, className } = props;
        const match = /language-(\w+)/.exec(className || "");
        const language = match ? match[1] : "plaintext";
        if (props.node?.properties.isInline) {
          return <InlineCode c={children?.toString() || ""}></InlineCode>;
        }

        return <CodeBlock language={language} content={String(children)} handleCopy={handleCopy}></CodeBlock>;
      },
    };
  }, [handleCopy]);

  return (
    <div className={clsx(style["kwaipilot-markdown"])}>
      <ReactMarkdown remarkPlugins={[RemarkGfm]} components={components} rehypePlugins={[addInlineFlagToCode]}>
        {content}
      </ReactMarkdown>
    </div>
  );
};
