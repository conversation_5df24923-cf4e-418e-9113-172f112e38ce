import { InternalLocalMessage, isHumanMessage } from "shared/lib/agent";
import { useComposerState } from "../../context/ComposerStateContext";
import { UserInputTextArea } from "../UserInputTextArea/UserInputTextArea";
import { HumanMessageReadonly } from "./HumanMessageReadonly";
import { Box, BoxProps, Button, ButtonProps, Tooltip } from "@chakra-ui/react";
import { useDesignToken } from "@/hooks/useDesignToken";
import { useCallback, useMemo, useRef, useEffect } from "react";
import { kwaiPilotBridgeAPI } from "@/bridge";

import { cn, withProviders } from "@udecode/cn";
import { isCheckpointCreatedMessage, isTerminalMessage, isToolEditFileMessage } from "shared/lib/agent/isToolMessage";
import { RestoreAndSendDialog, RestoreAndSendDialogContext, useRestoreAndSendDialog } from "./RestoreAndSendDialog";
import { RestoreConfirmDialog, RestoreConfirmDialogContext, useRestoreConfirmDialog } from "./RestoreConfirmDialog";
import { SendConfirmDialog, SendConfirmDialogContext, useSendConfirmDialog } from "./SendConfirmDialog";
import eventBus from "@/utils/eventBus";
import { useAsync } from "react-use";

function IconRestore() {
  return (
    <svg width="15" height="14" viewBox="0 0 15 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M1.56491 7.8751C1.56593 8.86107 1.95805 9.80636 2.65523 10.5035C3.35241 11.2007 4.2977 11.5928 5.28366 11.5939H10.0962C10.2702 11.5939 10.4371 11.5247 10.5602 11.4016C10.6833 11.2786 10.7524 11.1117 10.7524 10.9376C10.7524 10.7636 10.6833 10.5966 10.5602 10.4736C10.4371 10.3505 10.2702 10.2814 10.0962 10.2814H5.28366C4.64549 10.2814 4.03345 10.0278 3.58219 9.57658C3.13093 9.12532 2.87741 8.51328 2.87741 7.8751C2.87741 7.23693 3.13093 6.62489 3.58219 6.17363C4.03345 5.72237 4.64549 5.46885 5.28366 5.46885H11.1352L9.63023 6.97331C9.50694 7.09659 9.43768 7.2638 9.43768 7.43815C9.43768 7.6125 9.50694 7.77971 9.63023 7.90299C9.75351 8.02628 9.92072 8.09554 10.0951 8.09554C10.2694 8.09554 10.4366 8.02628 10.5599 7.90299L13.1849 5.27799C13.2461 5.21703 13.2946 5.14458 13.3278 5.06481C13.3609 4.98504 13.3779 4.89952 13.3779 4.81315C13.3779 4.72678 13.3609 4.64126 13.3278 4.56149C13.2946 4.48172 13.2461 4.40927 13.1849 4.34831L10.5599 1.72331C10.4366 1.60002 10.2694 1.53076 10.0951 1.53076C9.92072 1.53076 9.75351 1.60002 9.63023 1.72331C9.50694 1.84659 9.43768 2.0138 9.43768 2.18815C9.43768 2.3625 9.50694 2.52971 9.63023 2.65299L11.1352 4.15635H5.28366C4.29774 4.15751 3.35254 4.54968 2.65539 5.24683C1.95824 5.94398 1.56607 6.88918 1.56491 7.8751Z" fill="currentColor" />
    </svg>
  );
}

function RestoreButton({ children, className, ...rest }: ButtonProps) {
  const { tokens } = useDesignToken();
  const multiWorkspaceLabel = "在 VSCode 工作区模式下暂不支持对话回退功能";
  const { value: workspaceFileUri } = useAsync(() => kwaiPilotBridgeAPI.extensionComposer.$getWorkspaceFile(), []);
  const isVscodeWorkspace = useMemo(() => {
    return Boolean(workspaceFileUri);
  }, [workspaceFileUri]);
  return (
    <Tooltip label={isVscodeWorkspace ? multiWorkspaceLabel : "回退到本轮对话发起前"} placement="top-start">
      <Button
        display="inline-flex"
        rounded="4px"
        fontSize={12}
        alignItems="center"
        height="auto"
        fontWeight="normal"
        gap={1}
        lineHeight="18px"
        py="1px"
        px="6px"
        color={tokens.colorTextCommonSecondary}
        backgroundColor="rgba(255, 255, 255, 0.16)"
        className={cn(" border-solid border border-border-common", className)}
        backdropFilter="blur(5px)"
        disabled={isVscodeWorkspace}
        {...rest}
      >
        {children || (
          <>
            <IconRestore />
            <span>回退当前</span>
          </>
        )}
      </Button>
    </Tooltip>
  );
}

export const HumanMessage = withProviders(RestoreConfirmDialogContext, RestoreAndSendDialogContext, SendConfirmDialogContext)(function HumanMessage({ data, ...rest }: { data: InternalLocalMessage } & BoxProps) {
  const { editingMessageTs, currentMessageTs, localMessages, isCurrentWorkspaceSession, isStreaming } = useComposerState();

  const { invoke: invokeRestoreConfirmDialog, isOpen: isRestoreConfirmDialogOpen } = useRestoreConfirmDialog();
  const { isOpen: isRestoreAndSendDialogOpen } = useRestoreAndSendDialog();
  const { isOpen: isSendConfirmDialogOpen } = useSendConfirmDialog();

  const isEditing = editingMessageTs === data.ts;
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isEditing) {
      setTimeout(() => {
        const container = containerRef.current;
        if (container) {
          (container as any).scrollIntoViewIfNeeded();
        }
      }, 0);
    }
  }, [isEditing]);

  const humanMessageIndex = useMemo(() => localMessages.findIndex(v => v.ts === data.ts), [localMessages, data.ts]);

  const relatedCheckpointMessage = useMemo(() => {
    if (humanMessageIndex === -1) {
      return null;
    }
    const prediction = localMessages[humanMessageIndex + 1];
    if (prediction && isCheckpointCreatedMessage(prediction)) {
      return prediction;
    }
    return null;
  }, [localMessages, humanMessageIndex]);

  const showCheckpoint = useMemo(() => {
    return localMessages.slice(humanMessageIndex + 1).some(v => isToolEditFileMessage(v) || isTerminalMessage(v));
  }, [localMessages, humanMessageIndex]);

  const haveCheckpoint = Boolean(relatedCheckpointMessage);

  const onConfirmRestore = useCallback(async () => {
    if (!relatedCheckpointMessage?.lastCheckpointHash) {
      kwaiPilotBridgeAPI.showToast({
        message: "没有找到相关的检查点",
        level: "error",
      });
      return;
    }
    const humanMessage = localMessages[humanMessageIndex];
    if (!humanMessage || !isHumanMessage(humanMessage)) {
      kwaiPilotBridgeAPI.showToast({
        message: "没有找到相关的消息",
        level: "error",
      });
      return;
    }

    await kwaiPilotBridgeAPI.extensionComposer.$restoreCheckpoint({
      humanMessageTs: humanMessage.ts,
      restoreCommitHash: relatedCheckpointMessage.lastCheckpointHash,
      updateStateImmediately: true,
    });
    eventBus.emit("composer:onResetCheckpoint", {
      humanMessage,
    });
  }, [relatedCheckpointMessage?.lastCheckpointHash, localMessages, humanMessageIndex]);

  const onRestoreButtonClick = useCallback(() => {
    invokeRestoreConfirmDialog().then((reason) => {
      if (reason === "restore") {
        onConfirmRestore();
      }
    });
  }, [invokeRestoreConfirmDialog, onConfirmRestore]);

  return (
    <>
      <Box {...rest} role="group" ref={containerRef}>
        {isEditing
          ? <UserInputTextArea localMessage={data} hiddenApplyStatus role="conversation" />
          : <HumanMessageReadonly data={data} />}

        {isCurrentWorkspaceSession && haveCheckpoint && showCheckpoint && !isStreaming && (
          <>
            {!isRestoreConfirmDialogOpen
            && !isRestoreAndSendDialogOpen
            && !isSendConfirmDialogOpen
            && !isEditing
            && (currentMessageTs && currentMessageTs === relatedCheckpointMessage?.ts
              ? (
                  <></>
            // {/* <RedoButton onClick={onRedoButtonClick} mt={2} /> */}
                )
              : (
                  <RestoreButton
                    onClick={onRestoreButtonClick}
                    {...isEditing ? { mt: 2 } : { position: "absolute", bottom: 0, left: 0 }}
                    opacity={0}
                    _groupHover={{
                      opacity: 1,
                    }}
                    zIndex={10}
                  />
                ))}
          </>
        )}
      </Box>

      <RestoreConfirmDialog />
      <RestoreAndSendDialog />
      <SendConfirmDialog />
    </>
  );
});
