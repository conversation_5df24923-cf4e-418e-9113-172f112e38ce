import { Box, CloseButton, Flex, useOutsideClick } from "@chakra-ui/react";
import { createContext, useCallback, useContext, useMemo, useRef } from "react";
import { DialogButton } from "../DialogButton";

import KidIcon from "@kid/enterprise-icon/icon/output/Icon";
import IconWarn from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_warn_surface";
import { useDisclosureWithReason, UseDisclosureWithReasonReturn } from "./useDisclosureWithReason";

export type SendConfirmDialogResult = "cancel" | "confirm";

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface ContextValue extends UseDisclosureWithReasonReturn<SendConfirmDialogResult> {
}

const _SendConfirmDialogContext = createContext<ContextValue | null>(null);

export function useSendConfirmDialog(): {
  invoke(): Promise<SendConfirmDialogResult>;
  isOpen: boolean;
} {
  const context = useContext(_SendConfirmDialogContext);
  if (!context) {
    throw new Error("useSendConfirmDialog must be used within a SendConfirmDialogContext");
  }
  const invoke = useCallback(() => {
    return context.onOpen().settled;
  }, [context]);
  return {
    invoke,
    isOpen: context.isOpen,
  };
}

export function SendConfirmDialogContext({
  children,
}: {
  children: React.ReactNode;
}) {
  const { isOpen, onOpen, onClose } = useDisclosureWithReason<SendConfirmDialogResult>();
  const provided = useMemo<ContextValue>(() => {
    return {
      isOpen,
      onOpen,
      onClose,
    };
  }, [isOpen, onOpen, onClose]);
  return (
    <_SendConfirmDialogContext.Provider value={provided}>
      {children}
    </_SendConfirmDialogContext.Provider>
  );
}

export function SendConfirmDialog() {
  const context = useContext(_SendConfirmDialogContext);
  if (!context) {
    throw new Error("SendConfirmDialog must be used within a SendConfirmDialogContext");
  }
  const { isOpen, onClose } = context;
  const onConfirm = useCallback(() => {
    onClose("confirm");
  }, [onClose]);
  const onCancel = useCallback(() => {
    onClose("cancel");
  }, [onClose]);

  const ref = useRef<HTMLDivElement>(null);
  useOutsideClick({
    ref,
    handler: () => {
      onClose("cancel");
    },
  });
  return isOpen && (
    <Box ref={ref} mt={2} className=" border-border-common border border-solid rounded-lg relative">
      <Flex alignItems="center" gap={1} py={3} px={4}>
        <KidIcon config={IconWarn} color="#FFBB26" size={16} />
        <span className=" text-[13px] leading-[18px] font-medium">确定要发送此消息吗？</span>
      </Flex>
      <Box className="  text-text-common-secondary text-[13px] leading-[20px] pl-9 pr-4 pb-3">
        后续的对话记录将会被清除，是否继续？
      </Box>
      <Flex justify="right" gap={2} px={4} pb={3}>
        <DialogButton onClick={onCancel}>
          取消
        </DialogButton>
        <DialogButton isPrimary onClick={onConfirm}>
          继续
        </DialogButton>
      </Flex>
      <CloseButton size="sm" className=" absolute top-2 right-2" onClick={onCancel} />
    </Box>
  );
}
