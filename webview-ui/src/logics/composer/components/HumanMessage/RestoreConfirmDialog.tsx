import { Flex, Box, CloseButton, useOutsideClick } from "@chakra-ui/react";
import KidIcon from "@kid/enterprise-icon/icon/output/Icon";
import IconWarn from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_warn_surface";
import { DialogButton } from "../DialogButton";
import { createContext, useContext, useRef } from "react";
import { useDisclosureWithReason, UseDisclosureWithReasonReturn } from "./useDisclosureWithReason";

export type RestoreConfirmDialogResult = "cancel" | "restore";

const _RestoreConfirmDialogContext = createContext<UseDisclosureWithReasonReturn<RestoreConfirmDialogResult> | null>(null);

export function RestoreConfirmDialogContext({ children }: { children: React.ReactNode }) {
  const { isOpen, onOpen, onClose } = useDisclosureWithReason<RestoreConfirmDialogResult>();

  return (
    <_RestoreConfirmDialogContext.Provider value={{ isOpen, onOpen, onClose }}>
      {children}
    </_RestoreConfirmDialogContext.Provider>
  );
}

function useRestoreAndSendDialogContext() {
  const context = useContext(_RestoreConfirmDialogContext);
  if (!context) {
    throw new Error("RestoreConfirmDialogContext is not found");
  }
  return context;
}

export function RestoreConfirmDialog() {
  const { isOpen, onClose } = useRestoreAndSendDialogContext();
  const ref = useRef<HTMLDivElement>(null);
  useOutsideClick({
    ref,
    handler: () => {
      onClose("cancel");
    },
  });
  return isOpen && (
    <Box ref={ref} mt={2} className=" border-border-common border border-solid rounded-lg relative">
      <Flex alignItems="center" gap={1} py={3} px={4}>
        <KidIcon config={IconWarn} color="#FFBB26" size={16} />
        <span className=" text-[13px] leading-[18px] font-medium">是否回退至当前回答</span>
      </Flex>
      <Box className="  text-text-common-secondary text-[13px] leading-[20px] pl-9 pr-4 pb-3">
        所有代码变更都将被还原到该版本之前，是否继续？
      </Box>
      <Flex justify="right" gap={2} px={4} pb={3}>
        <DialogButton onClick={() => onClose("cancel")}>
          取消
        </DialogButton>
        <DialogButton isPrimary onClick={() => onClose("restore")}>
          继续
        </DialogButton>
      </Flex>
      <CloseButton size="sm" className=" absolute top-2 right-2" onClick={() => onClose("cancel")} />
    </Box>
  );
}

export function useRestoreConfirmDialog() {
  const context = useContext(_RestoreConfirmDialogContext);
  if (!context) {
    throw new Error("RestoreConfirmDialogContext is not found");
  }
  return {
    invoke: () => {
      return context.onOpen().settled;
    },
    isOpen: context.isOpen,
  };
}
