import { RichEditor } from "../UserInputTextArea/RichEditor";
import { LexicalEditor } from "lexical";
import { useCallback, useMemo, useRef } from "react";
import { InternalLocalMessage, isHumanMessage } from "shared/lib/agent";
import { useComposerState } from "../../context/ComposerStateContext";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { ContextHeaderReadonly } from "../UserInputTextArea/ContextHeader";
import { useBridgeObservableAPI } from "@/bridge/useBridgeObservableAPI";
import { Alert, AlertDescription, AlertTitle, chakra, CloseButton, ToastProps, useToast } from "@chakra-ui/react";
import clsx from "clsx";
import { vsCss } from "@/style/vscode";

/**
 * 贴近原生 vscode ui 的 toast 样式, 验证没问题后集成到全局 provider 中
 * @param props
 * @returns
 */
const VsCodeToast: React.FC<ToastProps> = (props) => {
  const {
    status,
    variant = "solid",
    id,
    title,
    isClosable,
    onClose,
    description,
    colorScheme,
    // icon,
  } = props;

  const ids = id
    ? {
        root: `toast-${id}`,
        title: `toast-${id}-title`,
        description: `toast-${id}-description`,
      }
    : undefined;

  return (
    <Alert
      addRole={false}
      status={status}
      variant={variant}
      id={ids?.root}
      alignItems="start"
      borderRadius="md"
      boxShadow="lg"
      paddingEnd={8}
      textAlign="start"
      width="auto"
      textColor={vsCss.notificationsForeground}
      borderColor={vsCss.notificationToastBorder}
      borderWidth={1}
      borderStyle="solid"
      bgColor={vsCss.notificationsBackground}
      px="10px"
      py="10px"
      colorScheme={colorScheme}
    >
      {/* <AlertIcon>{icon}</AlertIcon> */}
      <chakra.div flex="1" maxWidth="100%">
        {title && <AlertTitle id={ids?.title}>{title}</AlertTitle>}
        {description && (
          <AlertDescription id={ids?.description} display="block">
            {description}
          </AlertDescription>
        )}
      </chakra.div>
      {isClosable && (
        <CloseButton
          size="sm"
          onClick={onClose}
          position="absolute"
          insetEnd={1}
          top={1}
        />
      )}
    </Alert>
  );
};

export function HumanMessageReadonly({ data }: { data: InternalLocalMessage }) {
  if (!isHumanMessage(data)) {
    throw new Error("data is not a human message");
  }
  const { sessionId, isCurrentWorkspaceSession, isStreaming } = useComposerState();
  const { editorState } = data;
  const panelRef = useRef<HTMLDivElement>(null);
  const editorRef = useRef<LexicalEditor>(null);
  const initialEditorState = useMemo(() => editorState ? JSON.stringify(editorState) : undefined, [editorState]);

  const toast = useToast({
    render: VsCodeToast,
  });

  const copySessionId = useCallback(() => {
    navigator.clipboard.writeText(sessionId);
    toast({
      title: "复制成功",
      description: `sessionId已复制到剪贴板 ${sessionId}`,
    });
  }, [sessionId, toast]);

  const copyChatId = useCallback(() => {
    navigator.clipboard.writeText(data.chatId || "");
    toast({
      title: "复制成功",
      description: `chatId已复制到剪贴板 ${data.chatId}`,
    });
  }, [data.chatId, toast]);

  const transformToEditable = useCallback(async (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isCurrentWorkspaceSession) {
      return;
    }
    if (isStreaming) {
      return;
    }
    // 防止 conversation onClick setEditingMessageTs(undefined) 触发 https://team.corp.kuaishou.com/task/B2489555
    e.stopPropagation();
    await kwaiPilotBridgeAPI.extensionComposer.$setEditingMessageTs(data.ts);
  }, [data.ts, isCurrentWorkspaceSession, isStreaming]);

  const isDeveloperMode = useBridgeObservableAPI("isDeveloperMode");

  return (
    <>
      <div className="flex ml-[24px] justify-end">
        <div
          className={clsx(
            "bg-bg-user-default rounded-l-[12px] rounded-tr-[2px] rounded-br-[12px] p-[12px] max-w-full")}
          onClick={transformToEditable}
        >
          <div ref={panelRef}></div>
          {data.contextItems?.length ? <ContextHeaderReadonly pb={3} persistentNodes={data.contextItems || []} onClick={e => /* 防止transformToEditable */e.stopPropagation()} /> : null}
          <RichEditor
            editable={false}
            editorRef={editorRef}
            onSubmit={async () => { }}
            panelRef={panelRef}
            changeEditorState={() => { }}
            disabled={false}
            editorClassName=" w-fit p-0"
            initialEditorState={initialEditorState}
            customOptions={{
              slashCommandEnabled: false,
              sharpCommandEnabled: false,
            }}
          />
        </div>

      </div>
      {isDeveloperMode && (
        <div>
          sessionId:
          <span onClick={copySessionId}>
            {data.sessionId}
          </span>
          <br />
          chatId:
          <span onClick={copyChatId}>
            {data.chatId}
          </span>
        </div>
      )}
    </>
  );
};
