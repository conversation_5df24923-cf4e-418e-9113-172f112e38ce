import { Popover<PERSON>ontent, Popover, PopoverTrigger, useDisclosure } from "@chakra-ui/react";
import { useComposerState } from "../../context/ComposerStateContext";
import { useCallback, useMemo } from "react";
import { SupportedModelEnum, SupportedModels } from "shared/lib/agent/supportedModels";
import { kwaiPilotBridgeAPI } from "@/bridge";
import clsx from "clsx";
import { ArrowDownIcon } from "@chakra-ui/icons";
import { CustomScrollBar } from "@/components/CustomScrollbar";

export function ModelSelector({ className }: { className?: string }) {
  const { onOpen, onClose, isOpen } = useDisclosure();
  const { userPreferredModel: currentModel } = useComposerState();
  const modelList = useMemo(() => [
    { model: SupportedModelEnum.kwaipilotPro32k, label: "kwaipilot-pro-32k" },
    { model: SupportedModelEnum.claude3, label: "claude-3" },
  ], []);
  const handleSelect = useCallback(async (model: SupportedModels) => {
    await kwaiPilotBridgeAPI.extensionComposer.$setCurrentModel(model);
    onClose();
  }, [onClose]);

  const chatModelDetail = useMemo(
    () => modelList.find(model => model.model === currentModel),
    [currentModel, modelList],
  );

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      strategy="fixed"
      placement="top-start"
      closeOnBlur={true}
      trigger="click"
      boundary="scrollParent"
      computePositionOnMount={true}
      eventListeners={true}
      flip={true}
    >
      <PopoverTrigger>
        <div
          onMouseDown={e => e.preventDefault()}
          className={clsx(
            "flex-auto overflow-hidden flex gap-1 items-center cursor-pointer rounded hover:bg-bg-controls-hover",
            {
              "bg-bg-controls-hover": isOpen,
            },
            className,
          )}
        >

          <span
            className={clsx(
              "whitespace-nowrap text-text-common-secondary text-[13px] leading-[19.5px]",
              // css["model-area-item-text-hidden"],
              "truncate",
            )}
          >
            {chatModelDetail?.label}
          </span>
          <div
            className={clsx(
              "w-4 h-4 flex items-center justify-center transition-all text-icon-common-secondary",
              {
                "rotate-180": !isOpen,
              },
            )}
          >
            <ArrowDownIcon />
          </div>

        </div>
      </PopoverTrigger>
      <PopoverContent
        border="none"
        w="200px"
        sx={{
          "&:focus": {
            outline: "none",
          },
          "&:focus-visible": {
            outline: "none",
            boxShadow: "none",
          },
        }}
      >
        <CustomScrollBar
          suppressScrollX
          className="h-[224px] w-[220px] border border-border-common rounded bg-bg-fill"
        >
          <div className="p-1 w-full flex flex-col gap-1 rounded bg-bg-fill">
            {modelList.map((model, idx) => {
              return (
                <div
                  className={clsx(
                    "w-full rounded-sm px-3 py-2 hover:bg-bg-hover ",
                    {
                      "bg-bg-selected":
                        chatModelDetail?.model === model.model,
                    },
                    "cursor-pointer",
                  )}
                  onClick={() => handleSelect(model.model)}
                  key={idx}
                >
                  <div className="flex gap-1 items-center">
                    <div
                      className={clsx(" leading-[19.5px] text-[13px]", [
                        "text-text-common-primary",
                      ])}
                    >
                      {model.label}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </CustomScrollBar>
      </PopoverContent>
    </Popover>
  );
}
