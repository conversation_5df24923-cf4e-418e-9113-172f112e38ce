import { kwaiPilotBridgeAPI } from "@/bridge";
import { reportCopyMessage } from "@/http/api/feedback";
import { getHighLight } from "@/components/Dialog/Highlight";

import { reportUserAction, collectClick } from "@/utils/weblogger";

import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { useCallback, useMemo, useState, useRef, useEffect } from "react";
import { CodeHeaderBar } from "../components/CodeHeaderBar";

import ArrowDownIcon from "@/assets/codeblock/arrowDown.svg?react";
import { useComposerState } from "../context/ComposerStateContext";
import { DiffCode } from "@/logics/composer/components/DiffCode";
import { InternalLocalMessage_Tool_EditFile, WorkingSetEffect } from "shared/lib/agent";
import { diffLines } from "diff";
import { useVsEditorConfig } from "@/store/vsEditorConfig";
import { isEqual } from "lodash";

export interface DiffContent {
  code: string; // 处理后的代码（没有差异标记）
  addedLines: number[]; // 新增的行号（从0开始）
  deletedLines: number[]; // 删除的行号（从0开始）
  maxCharacterNumber: number; // 当前行最大字符数
}

/**
 * 对比两个内容并返回差异块
 * @param originalContent 原始内容
 * @param modifiedContent 修改后的内容
 * @returns 差异块数组
 */
export function compareContents(originalContent: string, modifiedContent: string): DiffContent {
  // 使用diffLines比较内容
  const changes = diffLines(originalContent, modifiedContent);
  const withDiffAddLineNumber: number[] = [];
  const withDiffDeletedLineNumber: number[] = [];
  let code = "";

  let modifiedLineNumber = 1; // 修改后的内容的当前行号（从1开始）
  let maxCharacterNumber = 0; // 当前行最大字符数

  // 遍历所有变化
  for (let i = 0; i < changes.length; i++) {
    const change = changes[i];
    const value = change.value;
    code += value;
    // 如果是添加的部分或者修改的部分
    if (change.added) {
      // 将新增的内容分割为行
      const addedLines = change.value.split("\n");
      // 如果最后一行是空行（来自换行符），则移除
      if (addedLines[addedLines.length - 1] === "") {
        addedLines.pop();
      }
      for (const line of addedLines) {
        if (line.length > maxCharacterNumber) {
          maxCharacterNumber = line.length;
        }
        withDiffAddLineNumber.push(modifiedLineNumber);
        modifiedLineNumber++;
      }
    }
    else if (change.removed) {
      const deletedLines = change.value.split("\n");

      if (deletedLines[deletedLines.length - 1] === "") {
        deletedLines.pop();
      }
      for (const line of deletedLines) {
        if (line.length > maxCharacterNumber) {
          maxCharacterNumber = line.length;
        }
        withDiffDeletedLineNumber.push(modifiedLineNumber);
        modifiedLineNumber++;
      }
    }
    else {
      // 未修改的部分，只需更新行号
      const lines = change.value.split("\n");
      if (lines[lines.length - 1] === "") {
        lines.pop();
      }
      for (const line of lines) {
        if (line.length > maxCharacterNumber) {
          maxCharacterNumber = line.length;
        }
        modifiedLineNumber++;
      }
    }
  }

  return {
    code,
    addedLines: withDiffAddLineNumber,
    deletedLines: withDiffDeletedLineNumber,
    maxCharacterNumber,
  };
}

const CanCollaspeCode = ({ handleClick, isCollapsed, codeLoading, lang, content }: { handleClick: () => void; isCollapsed: boolean; codeLoading: boolean; content: string; lang: string }) => {
  const [shouldShowCollapseButton, setShouldShowCollapseButton] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const maxHeight = 212; // 与 maxHeight 样式保持一致
  const editorConfig = useVsEditorConfig(state => state.editorConfig);

  useEffect(() => {
    // 检测内容高度是否超过最大高度
    if (contentRef.current) {
      const contentHeight = contentRef.current.scrollHeight;
      setShouldShowCollapseButton(contentHeight > maxHeight);
    }
  }, [content]);

  // 监听内容变化，自动滚动到底部
  useEffect(() => {
    if (contentRef.current) {
      if (codeLoading) {
        // 流式生成时滚动到底部
        contentRef.current.scrollTop = contentRef.current.scrollHeight;
      }
      else {
        // 加载完成后滚动到顶部
        contentRef.current.scrollTop = 0;
      }
    }
  }, [content, codeLoading]);

  const html = getHighLight({
    code: String(content), language: lang, theme: editorConfig.theme, transformers: [
      {
        pre(node) {
          node.properties.style = `background-color: transparent;padding-bottom: 20px;;padding-top: 8px;`;
        },
        code(node) {
          node.properties.style = `background-color: transparent;font-family: ${editorConfig.fontFamily};`;
        },
        line(node) {
          node.properties.style = `padding: 0 12px`;
        },
      },
    ],
  });

  return (
    <div className="">
      <div
        ref={contentRef}
        className="overflow-x-hidden"
        style={{
          overflowY: isCollapsed ? "hidden" : "scroll",
          maxHeight: isCollapsed ? `${maxHeight}px` : "none",
        }}
      >
        <div
          className="bg-[var(--vscode-editor-background)]"
          style={{
            overflowX: !(isCollapsed && shouldShowCollapseButton) ? "scroll" : "hidden",
          }}
          dangerouslySetInnerHTML={{ __html: html }}
        />
      </div>
      {shouldShowCollapseButton && (
        <div
          onClick={handleClick}
          className="bg-[var(--vscode-editor-background)] w-full h-[20px] cursor-pointer flex items-center justify-center text-[var(--vscode-foreground)]"
          style={{
            backdropFilter: isCollapsed ? "blur(0.5px)" : "none",
          }}
        >
          {isCollapsed
            ? <ArrowDownIcon className="w-[14px] h-[14px] " />
            : <ArrowDownIcon className="w-[14px] h-[14px] transform rotate-180" />}
        </div>
      )}
    </div>
  );
};

export const EditFile = () => {
  const { message, isToolError, isLast } = useComposerTaskContext();
  const { isStreaming } = useComposerState();
  const { partial, workingSetEffect } = message as InternalLocalMessage_Tool_EditFile;
  const { path: filepath, language, content } = JSON.parse(message.text || "");
  // const version = useMemo(() => [].filter((v: any) => v?.path === filepath).length, [filepath]);
  // const currentVersion = useMemo(() => version + 1, [version]);
  const lang = useMemo(() => (language && language !== "default") ? language : filepath?.split(".").pop(), [language, filepath]);
  const [diffContent, setDiffContent] = useState<DiffContent | undefined>(undefined);
  // diff视图切换
  const [showDiffView, setShowDiffView] = useState(false);

  const [autoSwitched, setAutoSwitched] = useState<boolean>(false);
  // 是否折叠
  const [isCollapsed, setIsCollapsed] = useState(true);
  // 跟踪上一个workingSetEffect状态
  const prevWorkingSetEffectRef = useRef<WorkingSetEffect | null>(null);

  // 处理差异视图切换
  const handleToggleDiffView = useCallback(() => {
    if (showDiffView) {
      setShowDiffView(false);
      return;
    }

    if (!filepath) {
      kwaiPilotBridgeAPI.showToast({
        message: "需要文件路径才能显示差异视图",
        level: "error",
      });
      return;
    }

    setShowDiffView(true);
    reportUserAction({ key: "codeBlockApply", type: "llmMsgCode" }, message.sessionId);
    collectClick("VS_DIFF_VIEW");
  }, [filepath, message.sessionId, showDiffView]);

  const handleCopy = useCallback(() => {
    navigator.clipboard.writeText(content.replace(/\n$/, ""));
    reportCopyMessage({
      isBlock: true,
      reply: content,
      chatId: message.ts.toString(),
      question: message.sessionId,
    });
    reportUserAction({ key: "copy", type: "llmMsgCode" }, message.sessionId);
    collectClick("VS_COPY_RESPONSE");
  }, [content, message.sessionId, message.ts]);

  const handleKeep = useCallback(() => {
    if (!filepath) {
      kwaiPilotBridgeAPI.showToast({
        message: "文件路径未找到",
        level: "error",
      });
      return;
    }

    kwaiPilotBridgeAPI.editor.keepDiff({ filepath });
  }, [filepath]);

  const handleUndo = useCallback(() => {
    if (!filepath) {
      kwaiPilotBridgeAPI.showToast({
        message: "文件路径未找到",
        level: "error",
      });
      return;
    }

    kwaiPilotBridgeAPI.editor.undoDiff({ filepath });
  }, [filepath]);

  const handleReapply = useCallback(async () => {
    if (!filepath) {
      kwaiPilotBridgeAPI.showToast({
        message: "文件路径未找到",
        level: "error",
      });
      return;
    }
    // setDiffContent(undefined);
    setShowDiffView(false);

    kwaiPilotBridgeAPI.editor.applyFile({
      message,
    });
  }, [filepath, message]);

  const handleToggleCollapse = useCallback(() => {
    setIsCollapsed(!isCollapsed);
  }, [isCollapsed]);

  useEffect(() => {
    const diff = workingSetEffect?.diffContent || undefined;
    if (!isEqual(prevWorkingSetEffectRef.current?.diffContent, workingSetEffect?.diffContent)) {
      setDiffContent(diff);
      prevWorkingSetEffectRef.current = workingSetEffect;
    }

    if ((diff?.addedLines.length || diff?.deletedLines.length) && !autoSwitched) {
    // 更新差异内容和视图状态
      setShowDiffView(true);
      setAutoSwitched(true);
      // 更新引用值
    }
  }, [workingSetEffect, autoSwitched]);

  return (
    <div className="rounded-[8px] overflow-hidden border border-border-common border-solid">
      <CodeHeaderBar
        codeLoading={Boolean(partial && isStreaming && isLast)}
        filepath={filepath}
        language={language}
        handleCopy={handleCopy}
        handleKeep={handleKeep}
        handleUndo={handleUndo}
        handleReapply={handleReapply}
        addLines={diffContent?.addedLines.length}
        delLines={diffContent?.deletedLines.length}
        isToolError={isToolError}
        showDiffView={showDiffView}
        handleToggleDiffView={handleToggleDiffView}
      />
      {!isToolError && content && (
        showDiffView
          ? (
              <DiffCode
                language={language}
                diffContent={diffContent}
                isCollapsed={isCollapsed}
                handleToggleCollapse={handleToggleCollapse}
              />
            )
          : <CanCollaspeCode handleClick={handleToggleCollapse} isCollapsed={isCollapsed} codeLoading={Boolean(partial && isStreaming && isLast)} content={content} lang={lang} />
      )}
    </div>
  );
};
