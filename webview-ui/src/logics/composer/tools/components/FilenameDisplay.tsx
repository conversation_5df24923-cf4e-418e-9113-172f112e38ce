import { useDesignToken } from "@/hooks/useDesignToken";
import { useColorMode } from "@chakra-ui/react";
import { Typography, ConfigProvider, TooltipProps } from "antd";
import { useMemo } from "react";
import { twMerge } from "tailwind-merge";

function HighlightText({ text, highlight }: { text: string;highlight: string }) {
  const parts = text.split(new RegExp(`(${highlight})`, "gi"));

  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";

  const style = useMemo(() => {
    const style: React.CSSProperties = {
      fontFeatureSettings: `'clig' off, 'liga' off`,
      fontWeight: "500",
    };

    if (isDark) {
      style.color = "#3077E2";
    }
    else {
      style.color = "#326BFB";
    }
    return style;
  }, [isDark]);

  return (
    <>
      {parts.map((part, index) =>
      // 不区分大小写地检查是否为高亮部分
        part.toLowerCase() === highlight.toLowerCase()
          ? (
              <span key={index} style={style}>
                {part}
              </span>
            )
          : (
              part
            ),
      )}
    </>
  );
}

/**
 * 展示文件名，主要是中间截断
 */
export function FilenameDisplay({
  filename,
  className,
  maxSuffixLenth,
  tooltip,
  highlight,
}: {
  filename: string;
  className?: string;
  maxSuffixLenth?: number;
  tooltip?: React.ReactNode | TooltipProps;
  highlight?: string;
}) {
  const filenameSuffixI = filename.length - Math.min(Math.floor(filename.length / 2), maxSuffixLenth || 15);
  const filenamePrefixPart = filename.slice(0, filenameSuffixI);
  const filenameSuffixPart = filename.slice(filenameSuffixI);
  const { tokens } = useDesignToken();

  return (
    <ConfigProvider theme={{
      token: {
        colorBgSpotlight: tokens.colorBgFill,
        colorText: tokens.colorTextCommonPrimary,
        colorTextLightSolid: tokens.colorTextCommonPrimary,
        fontFamily: "var(--vscode-font-family)",
      },
      cssVar: true,
    }}
    >

      <Typography.Text
        key={filename/* 用来触发 ellipse 的 onResize 重新计算，否则会导致溢出计算错误 */}
        ellipsis={{
          tooltip: tooltip ?? (
            <span className=" text-[12px] leading-[18px]">
              {filename}
            </span>
          ),
          suffix: highlight
            ? (<HighlightText text={filenameSuffixPart} highlight={highlight}></HighlightText>) as unknown as string
            : filenameSuffixPart,
        }}
        className={twMerge(" flex-auto text-[12px]  text-text-common-secondary whitespace-nowrap", className)}
      >
        {highlight ? <HighlightText text={filenamePrefixPart} highlight={highlight}></HighlightText> : filenamePrefixPart}
      </Typography.Text>
    </ConfigProvider>
  );
}
