import DarkSearchIcon from "@/assets/tools/dark/tool-search.svg?react";
import DarkEditIcon from "@/assets/tools/dark/tool-edit.svg?react";
import DarkReadIcon from "@/assets/tools/dark/tool-read.svg?react";

import LightSearchIcon from "@/assets/tools/light/tool-search.svg?react";
import LightEditIcon from "@/assets/tools/light/tool-edit.svg?react";
import LightReadIcon from "@/assets/tools/light/tool-read.svg?react";
import { useColorMode } from "@chakra-ui/react";

interface IconProps {
  type: "search" | "edit" | "read";
}

export const Icon = (props: IconProps) => {
  const className = "flex-shrink-0 translate-y-[0.8px] size-[14px]";
  const { type } = props;

  const { colorMode: theme } = useColorMode();

  const isDark = theme === "dark";

  if (isDark) {
    if (type === "search") {
      return <DarkSearchIcon className={className} />;
    }
    else if (type === "edit") {
      return <DarkEditIcon className={className} />;
    }
    else {
      return <DarkReadIcon className={className} />;
    }
  }
  else {
    if (type === "search") {
      return <LightSearchIcon className={className} />;
    }
    else if (type === "edit") {
      return <LightEditIcon className={className} />;
    }
    else {
      return <LightReadIcon className={className} />;
    }
  }
};
