import { kwaiPilotBridgeAPI } from "@/bridge";
import { useDesignToken } from "@/hooks/useDesignToken";
import { Box, Flex, Link, Show, Spinner, useColorMode } from "@chakra-ui/react";
import { useCallback, useState } from "react";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { COMMAND_OUTPUT_STRING, COMMAND_REQ_APP_STRING, TerminalTextStructure } from "shared/lib/agent";
import CodeBlock from "../components/CodeBlockBase";
import { isTerminalMessage } from "shared/lib/agent/isToolMessage";
import IconExternal from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_skip";
import KidIcon from "@kid/enterprise-icon/icon/output/Icon";
import { useComposerState } from "../context/ComposerStateContext";
import clsx from "clsx";
import { CustomScrollBar } from "@/components/CustomScrollbar";
import { Icon } from "@iconify/react";

function IconTerminal() {
  return (
    <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="flex-shrink-0">
      <rect x="0.888965" y="0.497119" width="13.475" height="13.475" rx="3.2375" fill="#1F2B39" />
      <rect x="0.888965" y="0.497119" width="13.475" height="13.475" rx="3.2375" stroke="#4D525B" stroke-width="0.525" />
      <g clipPath="url(#clip0_7137_6498)">
        <path d="M5.3501 8.1628L6.51043 7.23462L5.3501 6.30644C5.29963 6.26604 5.25761 6.2161 5.22644 6.15946C5.19527 6.10283 5.17557 6.04061 5.16844 5.97635C5.16132 5.9121 5.16692 5.84708 5.18493 5.78499C5.20294 5.7229 5.233 5.66497 5.2734 5.6145C5.3138 5.56403 5.36374 5.52201 5.42037 5.49085C5.47701 5.45968 5.53923 5.43997 5.60348 5.43285C5.66773 5.42573 5.73276 5.43133 5.79484 5.44934C5.85693 5.46735 5.91486 5.49741 5.96533 5.5378L7.60596 6.8503C7.66359 6.89641 7.71011 6.9549 7.74208 7.02142C7.77406 7.08795 7.79066 7.16081 7.79066 7.23462C7.79066 7.30843 7.77406 7.38129 7.74208 7.44782C7.71011 7.51434 7.66359 7.57282 7.60596 7.61894L5.96533 8.93144C5.8634 9.01302 5.73324 9.05077 5.60348 9.03639C5.47372 9.02201 5.35498 8.95666 5.2734 8.85474C5.19181 8.75281 5.15406 8.62265 5.16844 8.49288C5.18283 8.36312 5.24817 8.24439 5.3501 8.1628ZM8.28271 9.03931H9.59521C9.72575 9.03931 9.85094 8.98745 9.94324 8.89515C10.0355 8.80285 10.0874 8.67766 10.0874 8.54712C10.0874 8.41658 10.0355 8.29139 9.94324 8.19909C9.85094 8.10679 9.72575 8.05493 9.59521 8.05493H8.28271C8.15218 8.05493 8.02699 8.10679 7.93469 8.19909C7.84238 8.29139 7.79053 8.41658 7.79053 8.54712C7.79053 8.67766 7.84238 8.80285 7.93469 8.89515C8.02699 8.98745 8.15218 9.03931 8.28271 9.03931ZM12.0562 4.28149V10.1877C12.0562 10.4053 11.9697 10.614 11.8159 10.7678C11.6621 10.9216 11.4534 11.0081 11.2358 11.0081H4.01709C3.79953 11.0081 3.59088 10.9216 3.43704 10.7678C3.2832 10.614 3.19678 10.4053 3.19678 10.1877V4.28149C3.19678 4.06393 3.2832 3.85528 3.43704 3.70145C3.59088 3.54761 3.79953 3.46118 4.01709 3.46118H11.2358C11.4534 3.46118 11.6621 3.54761 11.8159 3.70145C11.9697 3.85528 12.0562 4.06393 12.0562 4.28149ZM11.0718 4.44556H4.18115V10.0237H11.0718V4.44556Z" fill="#62646B" />
      </g>
      <defs>
        <clipPath id="clip0_7137_6498">
          <rect width="10.5" height="10.5" fill="white" transform="translate(2.37646 1.98462)" />
        </clipPath>
      </defs>
    </svg>
  );
}

function IconTerminalLight() {
  return (
    <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="flex-shrink-0">
      <rect x="0.930469" y="0.489551" width="13.475" height="13.475" rx="3.2375" fill="white" />
      <rect x="0.930469" y="0.489551" width="13.475" height="13.475" rx="3.2375" stroke="#A9B3BE" stroke-width="0.525" />
      <g clip-path="url(#clip0_7829_5497)">
        <path d="M5.3916 8.15523L6.55193 7.22705L5.3916 6.29887C5.34113 6.25847 5.29911 6.20853 5.26795 6.15189C5.23678 6.09526 5.21707 6.03304 5.20995 5.96879C5.20283 5.90453 5.20843 5.83951 5.22644 5.77742C5.24444 5.71533 5.27451 5.6574 5.3149 5.60693C5.3553 5.55646 5.40524 5.51445 5.46188 5.48328C5.51851 5.45211 5.58073 5.4324 5.64498 5.42528C5.70924 5.41816 5.77426 5.42376 5.83635 5.44177C5.89844 5.45978 5.95637 5.48984 6.00684 5.53023L7.64746 6.84273C7.70509 6.88885 7.75162 6.94733 7.78359 7.01385C7.81556 7.08038 7.83216 7.15324 7.83216 7.22705C7.83216 7.30086 7.81556 7.37372 7.78359 7.44025C7.75162 7.50677 7.70509 7.56526 7.64746 7.61137L6.00684 8.92387C5.90491 9.00545 5.77475 9.04321 5.64498 9.02882C5.51522 9.01444 5.39649 8.9491 5.3149 8.84717C5.23332 8.74524 5.19556 8.61508 5.20995 8.48532C5.22433 8.35555 5.28967 8.23682 5.3916 8.15523ZM8.32422 9.03174H9.63672C9.76725 9.03174 9.89244 8.97988 9.98475 8.88758C10.0771 8.79528 10.1289 8.67009 10.1289 8.53955C10.1289 8.40901 10.0771 8.28382 9.98475 8.19152C9.89244 8.09922 9.76725 8.04736 9.63672 8.04736H8.32422C8.19368 8.04736 8.06849 8.09922 7.97619 8.19152C7.88389 8.28382 7.83203 8.40901 7.83203 8.53955C7.83203 8.67009 7.88389 8.79528 7.97619 8.88758C8.06849 8.97988 8.19368 9.03174 8.32422 9.03174ZM12.0977 4.27393V10.1802C12.0977 10.3977 12.0112 10.6064 11.8574 10.7602C11.7036 10.9141 11.4949 11.0005 11.2773 11.0005H4.05859C3.84103 11.0005 3.63238 10.9141 3.47855 10.7602C3.32471 10.6064 3.23828 10.3977 3.23828 10.1802V4.27393C3.23828 4.05637 3.32471 3.84772 3.47855 3.69388C3.63238 3.54004 3.84103 3.45361 4.05859 3.45361H11.2773C11.4949 3.45361 11.7036 3.54004 11.8574 3.69388C12.0112 3.84772 12.0977 4.05637 12.0977 4.27393ZM11.1133 4.43799H4.22266V10.0161H11.1133V4.43799Z" fill="#95A1AC" />
      </g>
      <defs>
        <clipPath id="clip0_7829_5497">
          <rect width="10.5" height="10.5" fill="white" transform="translate(2.41797 1.97705)" />
        </clipPath>
      </defs>
    </svg>

  );
}

function IconPendingDot() {
  return (
    <svg width="9" height="9" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle opacity="0.5" cx="4.78174" cy="4.23468" r="3" fill="#FFBB26" />
      <circle cx="4.78174" cy="4.23468" r="2" fill="#FFBB26" />
    </svg>
  );
}

const splitMessage = (text: string) => {
  const outputIndex = text.indexOf(COMMAND_OUTPUT_STRING);
  if (outputIndex === -1) {
    return { command: text, output: "" };
  }
  return {
    command: text.slice(0, outputIndex).trim(),
    output: text
      .slice(outputIndex + COMMAND_OUTPUT_STRING.length)
      .trim()
      .split("")
      .map((char) => {
        switch (char) {
          case "\t":
            return "→   ";
          case "\b":
            return "⌫";
          case "\f":
            return "⏏";
          case "\v":
            return "⇳";
          default:
            return char;
        }
      })
      .join(""),
  };
};

export function Terminal() {
  const { tokens } = useDesignToken();
  const { message, isLast } = useComposerTaskContext();
  const { currentTaskInterrupted } = useComposerState();
  const { colorMode: theme } = useColorMode();

  const [commandRunClicked, setCommandRunClicked] = useState(false);
  const [commandCancelClicked, setCommandCancelClicked] = useState(false);

  const isDark = theme === "dark";

  if (!isTerminalMessage(message)) {
    throw new Error("message is not a terminal message");
  }
  const terminalInfo = JSON.parse(message.text || "{}") as TerminalTextStructure;
  const { command: rawCommand, output } = splitMessage(terminalInfo.command || "");

  const requestsApproval = rawCommand.endsWith(COMMAND_REQ_APP_STRING);
  const command = requestsApproval ? rawCommand.slice(0, -COMMAND_REQ_APP_STRING.length) : rawCommand;

  /**
   * 点击运行终端
   */
  const onRunTerminalClick = useCallback(() => {
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "askResponse",
      askResponse: "yesButtonClicked",
      text: "",
    });
    setCommandRunClicked(true);
  }, []);

  const onSkipClick = useCallback(() => {
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "askResponse",
      askResponse: "noButtonClicked",
      text: "",
    });
    setCommandCancelClicked(true);
  }, []);

  const onOpenTerminalClick = useCallback(() => {
    kwaiPilotBridgeAPI.composerToggleTerminal();
  }, []);

  // const outputReceived = useMemo(() => message?.text?.includes(COMMAND_OUTPUT_STRING), [message]);

  const isCommandExecuting = ("outputMessage" in message && message.outputMessage?.partial) && isLast && commandRunClicked;

  return (
    <div className={clsx(" border-t-border-common border rounded border-solid ", isDark ? "bg-[#0E1825]" : "bg-[#fff]")}>
      <div className=" flex items-center gap-1 leading-[18px] text-[13px] py-2 px-3">
        { isDark
          ? <IconTerminal />
          : <IconTerminalLight></IconTerminalLight>}

        <Show breakpoint="(min-width: 320px)">
          <span className=" text-[13px] text-text-common-primary">
            命令行
          </span>
        </Show>
        {message.partial
          ? (
              <Flex className=" text-text-common-disable text-[12px] whitespace-nowrap" align="center" gap={1}>
                <Spinner size="xs" color="inherit" />
                <span>正在生成</span>
              </Flex>
            )
          : isLast && !currentTaskInterrupted
            && !commandRunClicked && !commandCancelClicked && (
            <div className=" ml-1 whitespace-nowrap">

              <span className=" text-text-common-disable flex items-center gap-1">
                <IconPendingDot />
                <Show breakpoint="(min-width: 380px)">

                  等待操作
                </Show>
              </span>
            </div>
          )}

        {!message.partial && isLast && (
          <Flex gap={2} ml="auto" align="center" flex="none">
            <Link

              textDecoration="none"
              fontSize={12}
              onClick={onOpenTerminalClick}
              gap={1}
              color={tokens.colorTextCommonSecondary}
              flexWrap="nowrap"
              display="flex"
              alignItems="center"
            >
              <KidIcon config={IconExternal} size={14} color="inherit" />
              打开终端
            </Link>
            {!currentTaskInterrupted && !commandRunClicked && !commandCancelClicked && (
              <>
                <div className=" h-[12px] w-[1px] bg-border-common rounded-sm"></div>
                <Link textDecoration="none" fontSize={12} onClick={onSkipClick} color={tokens.colorTextCommonSecondary}>
                  取消
                </Link>
                <button
                  className="  hover:opacity-90 text-[12px] font-medium flex items-center gap-1 text-white leading-[18px] py-[2px] px-1 rounded"
                  style={{
                    background: "linear-gradient(180deg, #46B8FF 0%, #069FFF 100%), linear-gradient(90deg, #89D1F9 0.53%, #75B9FF 98.08%), #DADFE7",
                  }}
                  onClick={onRunTerminalClick}
                >
                  <Icon icon="si:play-fill" className="text-[#E5ECF2]" />
                  <span>运行</span>
                </button>
              </>
            ) }
            {isCommandExecuting
            && (
              <button
                className="  hover:opacity-90 text-[12px] font-medium flex items-center gap-1 text-white leading-[18px] py-[2px] px-1 rounded"
                style={{
                  background: "linear-gradient(180deg, #46B8FF 0%, #069FFF 100%), linear-gradient(90deg, #89D1F9 0.53%, #75B9FF 98.08%), #DADFE7",
                }}
                onClick={onRunTerminalClick}
              >
                <Spinner size="xs"></Spinner>
                <span>运行</span>
              </button>
            )}
          </Flex>
        )}
      </div>
      <div>

        <CustomScrollBar className=" px-2 box-border py-2 overflow-auto max-w-full">
          <CodeBlock
            language="shellscript"
            source={command}
            forceWrap={true}
            clearPrebg={true}
            className="w-max"
          >
          </CodeBlock>
        </CustomScrollBar>
      </div>
      {terminalInfo.isBackground
        ? (
            <div className=" text-text-common-disable text-[12px] px-3 py-2">
              识别到为长时间命令，前往 vscode 终端面板观察日志
            </div>
          )
        : (
            <Box as="pre" px={3} py={2} fontSize="12px" lineHeight="18px" whiteSpace="pre-wrap">
              {output}
            </Box>
          )}

    </div>
  );
}
