import { SayTool } from "shared/lib/agent";
import { Container } from "./components/Container";
import { Icon } from "./components/Icon";
import { Title } from "./components/Title";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { Loading } from "./components/loading";
import { SubTitle } from "./components/SubTitle";
import { Error } from "./components/Error";
import { useCallback } from "react";
import { kwaiPilotBridgeAPI } from "@/bridge";

interface IProps {
  tool: SayTool;

}

export const ReadFile = (props: IProps) => {
  const tool = props.tool;
  const { shouldReadEntireFile, startLine, endLine } = tool;
  const { isToolExecuting, isToolError } = useComposerTaskContext();

  const openFile = useCallback(() => {
    if (tool.path) {
      if (!shouldReadEntireFile && startLine && endLine) {
        kwaiPilotBridgeAPI.editor.openFileToEditor(tool.path, startLine, endLine);
      }
      else {
        kwaiPilotBridgeAPI.editor.openFileToEditor(tool.path);
      }
    }
  }, [tool.path, shouldReadEntireFile, startLine, endLine]);

  return (
    <Container onClick={openFile} className="hover:bg-send-hover cursor-pointer">
      <Icon type="read"></Icon>
      <Title type={tool.tool}></Title>
      {!shouldReadEntireFile && startLine && endLine && <SubTitle content={`L${startLine}-${endLine}`}></SubTitle>}
      {isToolExecuting ? <Loading></Loading> : isToolError ? <Error></Error> : tool.path && <SubTitle content={tool.path}></SubTitle> }
    </Container>
  );
};
