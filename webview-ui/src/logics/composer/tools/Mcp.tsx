import { kwaiPilotBridgeAPI } from "@/bridge";
import { useDesignToken } from "@/hooks/useDesignToken";
import { Box, Flex, Link, Spinner, Tooltip, useColorMode } from "@chakra-ui/react";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useComposerTaskContext } from "../context/ComposerTaskContext";
import { McpTextStructure } from "shared/lib/agent";
import { isMcpMessage } from "shared/lib/agent/isToolMessage";
import { useComposerState } from "../context/ComposerStateContext";
import clsx from "clsx";
import KidIcon from "@kid/enterprise-icon/icon/output/Icon";
import IconArrowDown from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_smallarrow_down";
import { Error as ToolError } from "./components/Error";

function IconMcp() {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="0.2625" y="0.2625" width="13.475" height="13.475" rx="3.2375" fill="#1F2B39" />
      <rect x="0.2625" y="0.2625" width="13.475" height="13.475" rx="3.2375" stroke="#4D525B" stroke-width="0.525" />
      <path d="M5.6875 3.0625H3.9375C3.45425 3.0625 3.0625 3.45425 3.0625 3.9375V5.6875C3.0625 6.17075 3.45425 6.5625 3.9375 6.5625H5.6875C6.17075 6.5625 6.5625 6.17075 6.5625 5.6875V3.9375C6.5625 3.45425 6.17075 3.0625 5.6875 3.0625Z" stroke="#62646B" stroke-width="0.875" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M4.8125 6.56252V8.31252C4.8125 8.54458 4.90469 8.76714 5.06878 8.93123C5.23288 9.09533 5.45544 9.18752 5.6875 9.18752H7.4375" stroke="#62646B" stroke-width="0.875" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M10.0625 7.43748H8.3125C7.82925 7.43748 7.4375 7.82924 7.4375 8.31248V10.0625C7.4375 10.5457 7.82925 10.9375 8.3125 10.9375H10.0625C10.5457 10.9375 10.9375 10.5457 10.9375 10.0625V8.31248C10.9375 7.82924 10.5457 7.43748 10.0625 7.43748Z" stroke="#62646B" stroke-width="0.875" stroke-linecap="round" stroke-linejoin="round" />
    </svg>
  );
}

function IconMcpLight() {
  return (
    <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect x="0.2625" y="0.2625" width="13.475" height="13.475" rx="3.2375" fill="white" />
      <rect x="0.2625" y="0.2625" width="13.475" height="13.475" rx="3.2375" stroke="#A9B3BE" stroke-width="0.525" />
      <path d="M5.6875 3.0625H3.9375C3.45425 3.0625 3.0625 3.45425 3.0625 3.9375V5.6875C3.0625 6.17075 3.45425 6.5625 3.9375 6.5625H5.6875C6.17075 6.5625 6.5625 6.17075 6.5625 5.6875V3.9375C6.5625 3.45425 6.17075 3.0625 5.6875 3.0625Z" stroke="#95A1AC" stroke-width="0.875" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M4.8125 6.5625V8.3125C4.8125 8.54456 4.90469 8.76712 5.06878 8.93122C5.23288 9.09531 5.45544 9.1875 5.6875 9.1875H7.4375" stroke="#95A1AC" stroke-width="0.875" stroke-linecap="round" stroke-linejoin="round" />
      <path d="M10.0625 7.4375H8.3125C7.82925 7.4375 7.4375 7.82925 7.4375 8.3125V10.0625C7.4375 10.5457 7.82925 10.9375 8.3125 10.9375H10.0625C10.5457 10.9375 10.9375 10.5457 10.9375 10.0625V8.3125C10.9375 7.82925 10.5457 7.4375 10.0625 7.4375Z" stroke="#95A1AC" stroke-width="0.875" stroke-linecap="round" stroke-linejoin="round" />
    </svg>

  );
}

function IconPendingDot() {
  return (
    <svg width="9" height="9" viewBox="0 0 9 9" fill="none" xmlns="http://www.w3.org/2000/svg">
      <circle opacity="0.5" cx="4.78174" cy="4.23468" r="3" fill="#FFBB26" />
      <circle cx="4.78174" cy="4.23468" r="2" fill="#FFBB26" />
    </svg>
  );
}

function IconPlay() {
  return (
    <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fill-rule="evenodd" clipRule="evenodd" d="M13.9067 7.23468C13.9067 10.6174 11.1645 13.3597 7.78174 13.3597C4.39899 13.3597 1.65674 10.6174 1.65674 7.23468C1.65674 3.85194 4.39899 1.10968 7.78174 1.10968C11.1645 1.10968 13.9067 3.85194 13.9067 7.23468ZM12.5942 7.23468C12.5942 9.89255 10.4396 12.0472 7.78174 12.0472C5.12387 12.0472 2.96924 9.89255 2.96924 7.23468C2.96924 4.57681 5.12387 2.42218 7.78174 2.42218C10.4396 2.42218 12.5942 4.57681 12.5942 7.23468ZM8.69035 7.23468L7.30045 6.26174V8.20762L8.69035 7.23468ZM7.24626 4.83095C7.03066 4.81685 6.81533 4.86216 6.62369 4.96194C6.43206 5.06172 6.27145 5.21214 6.15936 5.39685C6.04727 5.58155 5.98798 5.79357 5.98795 6.00962V8.45984C5.98798 8.6759 6.04727 8.88781 6.15936 9.07251C6.27145 9.25722 6.43205 9.40764 6.62369 9.50742C6.81533 9.6072 7.03067 9.65251 7.24626 9.63842C7.46186 9.62432 7.66955 9.55132 7.84657 9.42744L9.59665 8.20238C9.75223 8.0935 9.87926 7.94871 9.96698 7.78029C10.0547 7.61184 10.1005 7.42451 10.1006 7.23458C10.1005 7.04465 10.0547 6.85753 9.96698 6.68907C9.87926 6.52065 9.75214 6.3758 9.59657 6.26692L7.84648 5.04186C7.66946 4.91799 7.46186 4.84504 7.24626 4.83095Z" fill="#E5ECF2" />
    </svg>
  );
}

export function Mcp() {
  const { tokens } = useDesignToken();
  const { message, isLast } = useComposerTaskContext();
  const { currentTaskInterrupted } = useComposerState();
  const { colorMode: theme } = useColorMode();
  const [isExpanded, setIsExpanded] = useState(false);
  const [mcpRunClicked, setMcpRunClicked] = useState(false);
  const [mcpCancelClicked, setMcpCancelClicked] = useState(false);

  const isDark = theme === "dark";

  if (!isMcpMessage(message)) {
    throw new Error("message is not a mcp message");
  }

  const mcpInfo = JSON.parse(message.text || "{}") as McpTextStructure;
  const resultReceived = useMemo(() => mcpInfo.output || mcpInfo.error, [mcpInfo.error, mcpInfo.output]);
  const isCommandExecuting = !resultReceived && isLast && mcpRunClicked;
  const isWaitingForExcute = !currentTaskInterrupted && !mcpRunClicked && !mcpCancelClicked;

  /**
   * 点击运行终端
   */
  const onRunMcpClick = useCallback(async () => {
    setMcpRunClicked(true);
    await kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "askResponse",
      askResponse: "yesButtonClicked",
      text: "",
    });
    setIsExpanded(false);
  }, []);

  const onSkipClick = useCallback(() => {
    kwaiPilotBridgeAPI.extensionComposer.$postMessageToComposerEngine({
      type: "askResponse",
      askResponse: "noButtonClicked",
      text: "",
    });
    setMcpCancelClicked(true);
    setIsExpanded(false);
  }, []);
  // console.log("xxxxxxxxxxxxx", message, currentTaskInterrupted, isCommandExecuting, isWaitingForExcute);

  useEffect(() => {
    if (resultReceived || currentTaskInterrupted) {
      setIsExpanded(false);
    }
    else {
      setIsExpanded(true);
    }
  }, [currentTaskInterrupted, resultReceived]);

  return (
    <div className={clsx("border border-solid border-border-common rounded-[8px]")}>
      <div className={clsx("flex items-center gap-1 leading-[18px] text-[13px] px-[12px] py-[8px] h-[34px] border-none cursor-pointer", isDark ? "bg-[#0E1825]" : "bg-[#fff]", isExpanded ? "rounded-t-[8px]" : "rounded-[8px]")} onClick={() => setIsExpanded(!isExpanded)}>
        { isDark
          ? <IconMcp />
          : <IconMcpLight></IconMcpLight>}
        <Tooltip label={mcpInfo.serverName}>
          <div className="h-[18px] max-w-[120px] truncate">
            {mcpInfo.serverName}
          </div>
        </Tooltip>
        <Tooltip label={mcpInfo.toolName}>
          <div className="w-0 flex-auto overflow-hidden h-[18px] max-w-full truncate text-text-common-tertiary">
            {mcpInfo.toolName}
          </div>
        </Tooltip>
        {message.partial && !currentTaskInterrupted
          ? (
              <Flex className=" text-text-common-disable text-[12px] whitespace-nowrap" align="center" gap={1}>
                <Spinner size="xs" color="inherit" />
                <span>正在生成</span>
              </Flex>
            )
          : isLast && !currentTaskInterrupted
            && !mcpRunClicked && !mcpCancelClicked && (
            <div className=" ml-1 whitespace-nowrap">
              <span className=" text-text-common-disable flex items-center gap-1">
                <IconPendingDot />
                等待操作
              </span>
            </div>
          )}
        {mcpInfo.error && <ToolError errorMsg="运行失败" />}
        {!message.partial && isLast && (
          <Flex gap={2} ml="auto" align="center" flex="none">
            {isWaitingForExcute && (
              <>
                <div className=" h-[12px] w-[1px] bg-border-common rounded-sm"></div>
                <Link textDecoration="none" fontSize={12} onClick={onSkipClick} color={tokens.colorTextCommonSecondary}>
                  取消
                </Link>
                <button
                  className="  hover:opacity-90 text-[12px] font-medium flex items-center gap-1 text-white leading-[18px] py-[2px] px-1 rounded"
                  style={{
                    background: "linear-gradient(180deg, #46B8FF 0%, #069FFF 100%), linear-gradient(90deg, #89D1F9 0.53%, #75B9FF 98.08%), #DADFE7",
                  }}
                  onClick={onRunMcpClick}
                >
                  <IconPlay />
                  <span>运行</span>
                </button>
              </>
            ) }
            {isCommandExecuting
            && (
              <button
                className="  hover:opacity-90 text-[12px] font-medium flex items-center gap-1 text-white leading-[18px] py-[2px] px-1 rounded"
                style={{
                  background: "linear-gradient(180deg, #46B8FF 0%, #069FFF 100%), linear-gradient(90deg, #89D1F9 0.53%, #75B9FF 98.08%), #DADFE7",
                }}
                onClick={onRunMcpClick}
              >
                <Spinner size="xs"></Spinner>
                <span>运行</span>
              </button>
            )}
          </Flex>
        )}
        {!message.partial && isLast && (isWaitingForExcute || isCommandExecuting)
          ? null
          : (
              <Flex
                align="center"
                w="14px"
                h="14px"
                justify="center"
                ml="auto"
                className={clsx(isExpanded ? "-rotate-180" : "", "transition-transform duration-200")}
              >
                <KidIcon config={IconArrowDown} size={14} color="#8A94A6" />
              </Flex>
            )}
      </div>
      <div className={clsx(
        "transition-all duration-200 ease-in-out overflow-auto",
        isExpanded ? "max-h-[240px] opacity-100" : "max-h-0 opacity-0",
      )}
      >
        <Box as="pre" fontSize="12px" lineHeight="18px" whiteSpace="pre-wrap" className="px-[12px] py-[8px]">
          {mcpInfo.arguments && (
            <div>
              <div className="text-text-common-disable mb-[4px]">参数</div>
              {mcpInfo.arguments}
            </div>
          )}
          {mcpInfo.output && (
            <div>
              <div className="text-text-common-disable my-[4px]">结果</div>
              {mcpInfo.output}
            </div>
          )}
        </Box>
      </div>
    </div>
  );
}
