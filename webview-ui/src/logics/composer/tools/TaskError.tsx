import IconError from "@kid/enterprise-icon/icon/output/common/system/common_system_error";
import KidIcon from "@kid/enterprise-icon/icon/output/Icon";
import { useComposerTaskContext } from "../context/ComposerTaskContext";

export function TaskError() {
  const { message } = useComposerTaskContext();
  return (
    <div className="text-[var(--vscode-charts-red)]">
      <div className="  flex items-center gap-1">
        <KidIcon config={IconError} size={14} color="inherit"></KidIcon>
        Error
      </div>
      <div className=" font-medium">{message.text}</div>

    </div>
  );
}
