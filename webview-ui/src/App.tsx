import { useColorMode } from "@chakra-ui/react";
import { useCallback, useEffect } from "react";
import { getRecordStoreByVendor } from "@/store/record";
import "@/style/global.css";
import "@/App.css";
import { initWeblogger, collectPV } from "@/utils/weblogger";
import { logger } from "@/utils/logger";

import { RouterProvider } from "react-router-dom";
import { router } from "@/router";
import { useInlineChatStore } from "@/store/inline-chat";
import eventBus from "@/utils/eventBus";
import { httpClient } from "@/http";
import { useChatListener } from "@/hooks/useChatListener";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { useUserListener } from "@/hooks/useUserListener";
import { UserInfo } from "@shared/types";
import { reportUserCopy } from "@/http/api/feedback";
import { useWorkspaceStore } from "@/store/workspace";
import useInlineChatListener from "@/hooks/useInlineChatListener";
import { Tips } from "./components/Tips";
import { useStore } from "zustand";
import { useVsEditorConfig } from "@/store/vsEditorConfig";
import { useRichEditPanelMenuStore } from "./store/richEditorPanelMenu";

/** 扩展来自 ext 注入的变量 */
declare global {
  interface Window {
    ide?: "vscode";
    vscMediaUrl: string;
    colorThemeName: "dark" | "light";
    webkit?: any;
    kwaipilotBridgeCall?: any;
    WKWebViewJavascriptBridge?: any;
    WKWVJBCallbacks?: any;
    bridge?: any;
    proxyUrl?: string;
    userInfo?: UserInfo;
  }
}

const App = () => {
  const { colorMode, setColorMode } = useColorMode();

  const setQuote = useInlineChatStore(state => state.setQuote);
  const clearInlineChatHistory = useInlineChatStore(
    state => state.clearInlineChatHistory,
  );
  const chatRecordStore = getRecordStoreByVendor("chat");
  const composerRecordStore = getRecordStoreByVendor("composer");
  const setEditorConfig = useVsEditorConfig(state => state.setEditorConfig);
  const setActiveSession = useStore(
    chatRecordStore,
    state => state.setActiveSession,
  );
  const activeSession = useStore(
    chatRecordStore,
    state => state.activeSession,
  );

  // FIXME: 输入框组件统一之后放到输入框组件中
  const setRuleFiles = useRichEditPanelMenuStore(state => state.setRuleFiles);
  useEffect(() => {
    const subscription = kwaiPilotBridgeAPI.observableAPI.rulesList().subscribe((list) => {
      setRuleFiles(list);
    });
    return () => {
      subscription.unsubscribe();
    };
  }, [setRuleFiles]);

  useEffect(() => {
    kwaiPilotBridgeAPI.onInlineChat((data) => {
      if (data) {
        clearInlineChatHistory();
        setQuote(data);
        eventBus.emit("inlineChat");
      }
      else {
        setActiveSession({
          value: activeSession,
          updateLocalStorage: false,
          navigateToChat: true,
          updateHistory: false,
        });
      }
    });
  }, [activeSession, clearInlineChatHistory, setActiveSession, setQuote]);

  useUserListener();
  useChatListener();
  useInlineChatListener();
  const updateTheme = (theme: "dark" | "light") => {
    if (theme === "dark") {
      document.body.classList.add("dark");
    }
    else {
      document.body.classList.remove("dark");
    }
  };
  useEffect(() => {
    // 添加热更新状态保持逻辑
    if (import.meta.hot) {
      import.meta.hot.accept(() => {
        // 保持状状态
        const chatStore = getRecordStoreByVendor("chat");
        const composerStore = getRecordStoreByVendor("composer");

        const chatState = chatStore.getState();
        const composerState = composerStore.getState();

        // 重新初始化后恢复状态
        setTimeout(() => {
          const newChatStore = getRecordStoreByVendor("chat");
          const newComposerStore = getRecordStoreByVendor("composer");

          newChatStore.setState(chatState);
          newComposerStore.setState(composerState);
        }, 0);
      });
    }

    kwaiPilotBridgeAPI.webviewBridgeReady();
    initWeblogger();

    window.setTimeout(async () => {
      let isWorkspace = false;
      try {
        isWorkspace = await Promise.race([
          kwaiPilotBridgeAPI.extensionComposer.$getWorkspaceFile().then(res => Boolean(res)),
          new Promise<never>((_, reject) => setTimeout(() => reject(new Error("timeout")), 5000)),
        ]);
      }
      catch (e) {
        isWorkspace = false;
      }
      collectPV("VS_HOME", {
        source: "html",
        isWorkspace,
      });
    }, 500);
    const handleCopy = () => {
      if (navigator.clipboard) {
        navigator.clipboard
          .readText()
          .then((text) => {
            logger.info("copy content", "App.tsx", {
              value: text,
            });
            reportUserCopy(text);
          })
          .catch((err) => {
            logger.error("copy error", "App.tsx", {
              err,
            });
          });
      }
    };
    const copyWatchTarget = document.getElementById("root");
    copyWatchTarget?.addEventListener("copy", handleCopy);
    return () => {
      copyWatchTarget?.removeEventListener("copy", handleCopy);
    };
  }, [chatRecordStore, composerRecordStore]);

  const setActiveProjectList = useWorkspaceStore(state => state.setAllRepos);

  const fetchModelList = useCallback(async () => {
    const modelList = await httpClient.getModelList();
    chatRecordStore.getState().setModelList(modelList);
    composerRecordStore.getState().setModelList(modelList);
  }, [chatRecordStore, composerRecordStore]);
  const fetchDoclList = useCallback(async () => {
    const docList = await httpClient.getDocList();
    chatRecordStore.getState().setDocList(docList);
    composerRecordStore.getState().setDocList(docList);
  }, [chatRecordStore, composerRecordStore]);

  useEffect(() => {
    fetchModelList();
    fetchDoclList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    const subscription = kwaiPilotBridgeAPI.observableAPI.currentTheme().subscribe((theme) => {
      if (theme !== colorMode) {
        updateTheme(theme);
        setColorMode(theme);
      }
      if (theme === "dark") {
        document.body.classList.add("dark");
      }
      else {
        document.body.classList.remove("dark");
      }
    });
    return () => {
      subscription.unsubscribe();
    };
  }, [colorMode, setColorMode]);
  // 监听主题变化
  useEffect(() => {
    kwaiPilotBridgeAPI.onActiveProjectChange(({ list }) => {
      setActiveProjectList(list);
    });
  }, [colorMode, setActiveProjectList, setColorMode]);

  useEffect(() => {
    kwaiPilotBridgeAPI
      .getConfig<boolean>("enableSummaryConversation")
      .then((data) => {
        chatRecordStore.getState().setSummaryConversation(data.value);
        composerRecordStore.getState().setSummaryConversation(data.value);
      });
  }, [chatRecordStore, composerRecordStore]);

  useEffect(() => {
    kwaiPilotBridgeAPI.getAndWatchEditorConfig((editorConfig) => {
      setEditorConfig(editorConfig);
    });
  }, [setEditorConfig]);

  const goInlineChat = useCallback(() => {
    router.navigate("/inline-chat");
  }, []);

  useEffect(() => {
    eventBus.on("inlineChat", goInlineChat);
    return () => {
      eventBus.off("inlineChat", goInlineChat);
    };
  }, [goInlineChat]);
  return (
    <div className="w-full h-screen flex flex-col">
      <Tips />
      <div className="h-0 flex-1">
        <RouterProvider router={router} />
      </div>
    </div>
  );
};

export default App;
