import * as ReactDOM from "react-dom/client";
import { <PERSON><PERSON>Provider } from "@chakra-ui/react";
import App from "@/App";
import theme from "@/utils/theme";
import CodeImage from "@/components/Prediction/codeImage";
import { initHighlighterInstance } from "./utils/highlighter";
import { ConfigProvider as AntConfigProvider } from "antd";

initHighlighterInstance().then(() => {
  const rootElement = document.getElementById("root") as Element;

  ReactDOM.createRoot(rootElement).render(
    <AntConfigProvider theme={{
      token: {
        fontFamily: "var(--vscode-font-family)",
      },
    }}
    >
      <ChakraProvider theme={theme}>
        <CodeImage />
        <App />
      </ChakraProvider>
    </AntConfigProvider>,
  );
});
