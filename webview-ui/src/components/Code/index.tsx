import { useColorMode } from "@chakra-ui/react";
import { ext2IconName } from "@/constant";
import AutoTooltip from "@/components/AutoTooltip";
import css from "./index.module.less";

interface Props {
  filename: string;
  startLine: number;
  endLine: number;
  content?: string;
  path: string;
}

export const Code = (props: Props) => {
  const { filename, startLine, endLine, path } = props;
  const name = filename.split("/").pop() || "";
  const { colorMode: theme } = useColorMode();
  const ext = filename.split(".").pop() ?? "ts";
  const src = `https://cdnfile.corp.kuaishou.com/kc/files/a/kwaipilot/kwaipilot-file-ext-icon/${
    ext2IconName[ext] ?? "typescript"
  }.svg`;

  return (
    <div
      className={`inline-dialog-item-bg-base-${theme} mt-2 rounded`}
      style={{
        boxShadow: "none",
      }}
    >
      <div
        className="w-full bg-bg-code-card rounded"
        style={{
          boxShadow: "none",
        }}
      >
        <div className="w-full flex flex-col rounded">
          <div
            className={`bg-bg-code-card px-[12px] py-[7px] ${css["title"]} rounded`}
          >
            <div
              className="flex gap-1 w-full rounded-t flex-row justify-between"
            >
              <div className="flex items-center gap-[4px] flex-auto overflow-hidden">
                <img className="w-[20px] h-[20px]" src={src} />
                <AutoTooltip
                  title={name}
                  className={`truncate ${css["title"]} leading-[18px] text-[13px] text-text-common-primary`}
                >
                  {name}
                </AutoTooltip>
              </div>
              <div
                className="whitespace-nowrap leading-[18px] text-text-common-primary"
              >
                Lines
                {" "}
                {startLine === endLine
                  ? startLine
                  : `${startLine + 1}-${endLine + 1}`}
              </div>
            </div>
            <div className="flex flex-auto overflow-hidden pl-[24px]">
              <AutoTooltip
                className="truncate leading-[18px] text-[13px] text-text-common-secondary"
                title={path}
              >
                {path}
              </AutoTooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
