import { useEffect, useState } from "react";

import "./index.css";
import { getHighlighterInstance } from "@/utils/highlighter";
import { useVsEditorConfig } from "@/store/vsEditorConfig";
import { getShikiTheme } from "@/utils/theme2shikiTheme";
import { logger } from "@/utils/logger";
import { getShikiLanguage } from "@/utils/language2shikiLang";
import { DecorationItem, type ShikiTransformer } from "shiki";

export interface IHighlightProps {
  language?: string;
  children: React.ReactNode;
  overflowX?: boolean;
}

export function Highlight(props: IHighlightProps) {
  const { language = "typescript", children, overflowX } = props;
  const editorConfig = useVsEditorConfig(state => state.editorConfig);

  const html = getHighLight({
    code: String(children), language, theme: editorConfig.theme, transformers: [
      {
        pre(node) {
          node.properties.style = `background-color: transparent;padding-bottom: 20px;;padding-top: 8px;`;
        },
        code(node) {
          node.properties.style = `background-color: transparent;font-family: ${editorConfig.fontFamily};`;
        },
        line(node) {
          node.properties.style = `padding: 0 12px`;
        },
      },
    ],
  });

  return (
    <div
      className="bg-[var(--vscode-editor-background)] rounded-[4px]"
      style={{
        overflowX: overflowX ? "scroll" : "hidden",
      }}
      dangerouslySetInnerHTML={{ __html: html }}
    />
  );
}

export function useHighlight({ children, language, transformers }: { children: React.ReactNode; language: string; transformers?: ShikiTransformer[] }) {
  const [html, setHtml] = useState<string>("");
  const editorConfig = useVsEditorConfig(state => state.editorConfig);
  // 只处理代码高亮，不重复创建 highlighter
  useEffect(() => {
    const updateHighlight = async () => {
      const code = String(children).replace(/\n$/, "");

      try {
        const html = getHighlighterInstance().codeToHtml(code, {
          theme: getShikiTheme(editorConfig.theme),
          lang: getShikiLanguage(language),
          transformers,
        });
        setHtml(html);
      }
      catch (error) {
        logger.error("Failed to highlight code:", "highlightComponent", {
          err: error,
        });
      }
    };

    updateHighlight();
  }, [children, editorConfig.theme, language, transformers]);

  return { html };
}

export function getHighLight({ code, language, theme, transformers, decorations }: { code: string; language: string; theme: string; transformers?: ShikiTransformer[]; decorations?: DecorationItem[] }) {
  try {
    code = String(code).replace(/\n$/, "");
    const html = getHighlighterInstance().codeToHtml(code, {
      theme: getShikiTheme(theme),
      lang: getShikiLanguage(language),
      transformers,
      decorations,
    });
    return html;
  }
  catch (error) {
    logger.error("Failed to highlight code:", "highlightComponent", {
      err: error,
    });
    return "";
  }
}
