@property --border-gradient-angle {
  syntax: "<angle>";
  inherits: true;
  initial-value: 0deg;
}
:root {
  --outer-radius: 8px;
  --border-size: 2px;
}

.stream-loading-container {
  padding: var(--border-size);
  height: 100%;
}
.stream-loading {
  background-image: conic-gradient(
    from var(--border-gradient-angle) at 50% 50%,
    #0084ff 0%,
    #00e1ff 25%,
    #914cff 50%,
    #0084ff 100%
  );
  border-radius: var(--outer-radius);
  background-size: contain;
  animation: buttonBorderSpin 1s linear infinite 0ms;
}

@keyframes buttonBorderSpin {
  0% {
    --border-gradient-angle: 0turn;
  }

  100% {
    --border-gradient-angle: 1turn;
  }
}

.stream-loading-content {
  background-color: black;
  border-radius: var(--outer-radius);
}
