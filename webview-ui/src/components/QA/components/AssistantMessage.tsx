import { ICachedMessage } from "@/utils/sessionUtils";
import { CardWithTag } from "./CardWithTag";
import { Actions } from "./Actions";
import { useEffect, useMemo, useState } from "react";
import { useRecordStore } from "@/store/record";
import { StreamLoading } from "@/components/Streamerloading";
import { Skeleton, Stack } from "@chakra-ui/react";

import { MarkdownRender } from "./MarkdownRender";
import { collectClick, reportUserAction } from "@/utils/weblogger";
import { IChatModelType } from "@shared/types/business";
import { ReportOpt } from "@shared/types/logger";
import { reportCopyMessage } from "@/http/api/feedback";
import { ICachedMessageAnswer, QAItem } from "@shared/types/chatHistory";

type AssisantMessageProps = {
  data: ICachedMessageAnswer[];
  onResend: (modelType?: IChatModelType) => void;
  onLikeOrUnlike: (item: ICachedMessage, isLike?: boolean) => void;
  isLast?: boolean;
  qaItem: QAItem;
};

export const AssisantMessage = (props: AssisantMessageProps) => {
  const {
    data: answers,
    onLikeOrUnlike,
    onResend,
    isLast,
    qaItem,
  } = props;
  const loadingStatu = useRecordStore(state => state.loadingStatu);
  // 数据index
  const total = answers.length;
  const [currentIndex, setCurrentIndex] = useState<number>(total - 1);
  const currentAnswer = answers[currentIndex];

  useEffect(() => {
    setCurrentIndex(answers.length - 1);
  }, [answers.length]);
  const handleNext = () => {
    setCurrentIndex(val => (val + 1) % total);
  };
  const handlePre = () => {
    setCurrentIndex(val => (val - 1 + total) % total);
  };
  const handleCopy = (item: ICachedMessage) => {
    const { reply, question, id } = item;
    const text = reply ?? "";
    navigator.clipboard.writeText(text.replace(/\n$/, ""));
    reportCopyMessage({
      isBlock: false,
      reply: text,
      chatId: id,
      question,
    });
    const parms: ReportOpt<"copy"> = {
      key: "copy",
      type: "llmMsg",
    };
    reportUserAction(parms, answers[0].id);
    collectClick("VS_COPY_RESPONSE");
  };

  // 当前加载中的数据是否和我有关？
  const isLoading = !!(
    loadingStatu
    && loadingStatu.id === currentAnswer.id
    && loadingStatu.status === "loading"
  );
  // 有内容返回之前
  const renderPreposition = () => {
    return (
      <div className="p-[12px]">
        <Stack>
          <Skeleton height="12px" width="60vw" />
          <Skeleton height="12px" width="30vw" />
        </Stack>
      </div>
    );
  };

  // 使用 useMemo 缓存渲染结果
  const memoizedContent = useMemo(
    () => (
      <CardWithTag modelType={currentAnswer?.modelType}>
        <div className="px-[12px] pt-[9px] overflow-y-hidden">
          <MarkdownRender
            answer={currentAnswer}
            qaItem={qaItem}
          />
        </div>
      </CardWithTag>
    ),
    [currentAnswer, qaItem],
  );

  return (
    <div className="flex" key={answers[currentIndex].id}>
      <div className="mr-[24px] flex flex-col gap-[4px] overflow-y-hidden rounded-[8px]">
        <StreamLoading
          isLoading={isLoading}
          id={currentAnswer.id}
          className="flex"
          key={currentAnswer.id}
        >
          {
            isLoading && !currentAnswer?.reply
              ? renderPreposition()
              : memoizedContent
          }
        </StreamLoading>
        {!isLoading && (
          <Actions
            total={total}
            current={currentIndex + 1}
            handleNext={handleNext}
            handlePre={handlePre}
            onLikeOrUnlike={isLike => onLikeOrUnlike(currentAnswer, isLike)}
            onResend={onResend}
            likeStatu={currentAnswer?.likeStatus}
            onCopy={() => handleCopy(currentAnswer)}
            isLast={isLast}
          />
        )}
      </div>
    </div>
  );
};
