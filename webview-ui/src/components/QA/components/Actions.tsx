import RenewIcon from "@/assets/renew.svg?react";
import LineIcon from "@/assets/line.svg?react";
import CopyIcon from "@/assets/copy.svg?react";
import LikeIcon from "@/assets/like.svg?react";
import LikeSelectedIcon from "@/assets/like-selected.svg?react";
import DislikeIcon from "@/assets/dislike.svg?react";
import DislikeSelectedIcon from "@/assets/dislike-selected.svg?react";
import LeftArrowIcon from "@/assets/left-arrow.svg?react";

import { SingleIcon } from "@/components/SingleIcon";
import { ModelSelector } from "@/components/TextArea/components/ModelSelector";
import { IChatModelType } from "@shared/types/business";
import clsx from "clsx";

import css from "./action.module.less";
import { ReocrdStoreVendorEnum, useRecordStore } from "@/store/record";

type ActionsProps = {
  total: number;
  current: number;
  handlePre: () => void;
  handleNext: () => void;
  onResend: (modelType?: IChatModelType) => void;
  onLikeOrUnlike: (isLike?: boolean) => void;
  likeStatu?: "like" | "unlike" | "cancel";
  onCopy: () => void;
  isLast?: boolean;
};

export const Actions = (props: ActionsProps) => {
  const {
    total,
    current,
    handleNext,
    handlePre,
    onResend,
    onLikeOrUnlike,
    likeStatu,
    onCopy,
    isLast,
  } = props;
  // 渲染一个自带统一样式的 单icon 24 * 24
  const selectModelCallback = (modelType: IChatModelType) => {
    onResend(modelType);
  };
  const recordVendor = useRecordStore(s => s.vendor);
  return (
    <div className="px-[8px] flex justify-between gap-6">
      <div className="flex gap-[6px]">
        {
          /* composer 模式下不显示重新生成 */ recordVendor
          === ReocrdStoreVendorEnum.chat && (
            <>
              <ModelSelector
                placement={isLast ? "top-start" : "bottom-start"}
                trigger="click"
                selectModelCallback={selectModelCallback}
                className="px-0 py-0"
              >
                <div className="flex cursor-pointer items-center h-[24px] gap-[4px] hover:bg-bg-controls-hover px-[2px] py-[4px] rounded-[4px]">
                  <RenewIcon />
                  <div
                    className={clsx(
                      "text-[13px] text-text-common-secondary",
                      css["hidden"],
                    )}
                  >
                    重新生成
                  </div>
                </div>
              </ModelSelector>

              <div className="flex items-center text-border-vertical">
                <LineIcon />
              </div>
            </>
          )
        }
        <SingleIcon onClick={onCopy} title="复制">
          <CopyIcon className="text-[#8D96A0]" />
        </SingleIcon>
        <div className="flex items-center text-border-vertical">
          <LineIcon />
        </div>
        <div className="flex gap-[6px]">
          <SingleIcon
            title="喜欢"
            onClick={() => {
              likeStatu === "like"
                ? onLikeOrUnlike(undefined)
                : onLikeOrUnlike(true);
            }}
          >
            {likeStatu === "like"
              ? (
                  <LikeSelectedIcon className="translate-y-[-1px]" />
                )
              : (
                  <LikeIcon className="translate-y-[-1px] text-[#8D96A0]" />
                )}
          </SingleIcon>
          <SingleIcon
            title="不喜欢"
            onClick={() => {
              likeStatu === "unlike"
                ? onLikeOrUnlike(undefined)
                : onLikeOrUnlike(false);
            }}
          >
            {likeStatu === "unlike"
              ? (
                  <DislikeSelectedIcon className="translate-y-[1px]" />
                )
              : (
                  <DislikeIcon className="translate-y-[1px] text-[#8D96A0]" />
                )}
          </SingleIcon>
        </div>
      </div>
      {total > 1 && (
        <div className="flex gap-[4px]">
          <SingleIcon onClick={handlePre} title="上一条">
            <LeftArrowIcon />
          </SingleIcon>
          <div className="text-text-common-secondary text-[13px] leading-[19.5px]  flex">
            <div className="min-w-[8px] flex items-center justify-center">
              {current}
            </div>
            <div className="flex items-center justify-center ">/</div>
            <div className="min-w-[8px] flex items-center justify-center">
              {total}
            </div>
          </div>
          <SingleIcon onClick={handleNext} title="下一条">
            <LeftArrowIcon className="rotate-180" />
          </SingleIcon>
        </div>
      )}
    </div>
  );
};
