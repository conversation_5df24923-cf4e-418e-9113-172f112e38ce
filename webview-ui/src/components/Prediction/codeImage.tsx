import { kwaiPilotBridgeAPI } from "@/bridge";
import React, { useCallback, useEffect, useRef, useState } from "react";
import { toPng } from "html-to-image";
import { diffWordsWithSpace } from "diff";
import "./index.css";

import { getShikiLanguage } from "@/utils/language2shikiLang";
import PreductLogoIcon from "@/assets/preduct-logo.svg?react";
import { getShikiTheme } from "@/utils/theme2shikiTheme";
import {
  addLineHeightToPreStyle,
  extractBackgroundColor,
  getHighlighterInstance,
} from "@/utils/highlighter";
import { useVsEditorConfig } from "@/store/vsEditorConfig";

const CodeImage: React.FC = () => {
  const [code, setCode] = useState<string>("");
  const imageContainer = useRef<HTMLDivElement>(null);
  const editorConfig = useVsEditorConfig(state => state.editorConfig);

  const handleAddedWords = (source: string, target: string) => {
    const diff = diffWordsWithSpace(source, target);
    const res: [number, number][] = [];
    let offset = 0;
    diff.forEach((part) => {
      if (part.added) {
        res.push([offset, offset + part.value.length]);
      }
      if (part.removed) {
        return;
      }
      offset += part.value.length;
    });
    return res;
  };
  const handlePredictionImage = useCallback(
    async (data: {
      sourceBlockContent: string;
      targetBlockContent: string;
      languageId: string;
    }) => {
      const addedOffsets = handleAddedWords(
        data.sourceBlockContent,
        data.targetBlockContent,
      );
      let renderCode = data.targetBlockContent;
      const isDeleteAll = data.targetBlockContent === "";
      if (isDeleteAll) {
        renderCode = data.sourceBlockContent;
      }
      if (!imageContainer.current) {
        return {
          dataUrl: "",
          backgroundColor: "",
        };
      }
      const html = getHighlighterInstance().codeToHtml(renderCode, {
        theme: getShikiTheme(editorConfig.theme),
        lang: getShikiLanguage(data.languageId),

        decorations: isDeleteAll
          ? [
              {
                start: 0,
                end: renderCode.length,
                properties: { class: "deleted-code" },
              },
            ]
          : addedOffsets.map(([start, end]) => ({
              start,
              end,
              properties: { class: "added-code" },
            })),
      });
      const transparentHtml = html.replace(
        "<code>",
        `<code style="font-size: ${editorConfig.fontSize + "px"};font-family:${
          editorConfig.fontFamily
        }; line-height: 1.5">`,
      );
      const backgroundColor = extractBackgroundColor(transparentHtml) ?? "";
      setCode(
        addLineHeightToPreStyle(transparentHtml, editorConfig.fontSize * 1.5),
      );

      // 使用 MutationObserver 等待DOM更新
      await new Promise<void>((resolve) => {
        const observer = new MutationObserver(() => {
          observer.disconnect();
          resolve();
        });

        if (imageContainer.current) {
          observer.observe(imageContainer.current, {
            childList: true,
            subtree: true,
            characterData: true,
          });
        }
      });
      const dataUrl = await toPng(imageContainer.current, {
        backgroundColor: "transparent",
        quality: 10,
        pixelRatio: 2,
        skipAutoScale: true,
        fontEmbedCSS: "",
        skipFonts: true,
      });
      setCode("");

      return { dataUrl, backgroundColor };
    },
    [editorConfig.fontFamily, editorConfig.fontSize, editorConfig.theme],
  );

  useEffect(() => {
    kwaiPilotBridgeAPI.onPredictionImage(handlePredictionImage);
  }, [handlePredictionImage]);

  return (
    <div className="fixed bottom-0 right-0 h-0 overflow-hidden">
      <div
        ref={imageContainer}
        className="opacity-1 relative rounded-lg overflow-hidden code-image-container"
      >
        <div
          className="px-1 pt-0 pb-1 min-w-[236px]"
          dangerouslySetInnerHTML={{ __html: code }}
        >
        </div>
        <div className="flex justify-between px-1 pt-1 pb-0 items-center border-t-[0.5px] border-border-horizontal">
          <div className="text-text-common-tertiary gap-2 font-medium flex items-center justify-center">
            采纳 (Tab)
            <div className="w-[1px] h-2.5 bg-border-horizontal" />
            取消 (Esc)
          </div>
          <div className="text-text-common-tertiary font-medium flex items-center justify-center">
            <PreductLogoIcon />
            &nbsp;· 智能预测
          </div>
        </div>
      </div>
    </div>
  );
};

export default CodeImage;
