// FIXME: eslint
import { useMemo } from "react";
import { SharpCommand } from "@shared/types";
import { Icon } from "@iconify/react";

interface IProps {
  isDark: boolean;
  type: string;
  status: "normal" | "disabled";
}

export const SharpCommandIcon: React.FC<IProps> = (props) => {
  const { isDark, type, status } = props;
  const iconColor = useMemo(() => {
    if (isDark) {
      if (status === "disabled") {
        return "#50575E";
      }
      else {
        return "#F7F8F8";
      }
    }
    else {
      if (status === "disabled") {
        return "#BBBDBF";
      }
      else {
        return "#656D76";
      }
    }
  }, [isDark, status]);
  if (type === SharpCommand.CURRENT_FILE) {
    return (
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M11.2806 8.96937C11.3505 9.03905 11.406 9.12185 11.4439 9.21301C11.4817 9.30417 11.5012 9.40191 11.5012 9.50062C11.5012 9.59933 11.4817 9.69707 11.4439 9.78824C11.406 9.8794 11.3505 9.9622 11.2806 10.0319L9.78063 11.5319C9.63973 11.6728 9.44863 11.7519 9.24938 11.7519C9.05012 11.7519 8.85902 11.6728 8.71813 11.5319C8.57723 11.391 8.49807 11.1999 8.49807 11.0006C8.49807 10.8014 8.57723 10.6103 8.71813 10.4694L9.6875 9.5L8.7175 8.53063C8.64774 8.46086 8.5924 8.37804 8.55464 8.28689C8.51688 8.19573 8.49745 8.09804 8.49745 7.99937C8.49745 7.90071 8.51688 7.80302 8.55464 7.71186C8.5924 7.62071 8.64774 7.53789 8.7175 7.46812C8.78726 7.39836 8.87009 7.34302 8.96124 7.30526C9.05239 7.26751 9.15009 7.24807 9.24875 7.24807C9.34741 7.24807 9.44511 7.26751 9.53626 7.30526C9.62741 7.34302 9.71024 7.39836 9.78 7.46812L11.2806 8.96937ZM7.28062 7.46938C7.21095 7.39946 7.12815 7.34398 7.03699 7.30612C6.94583 7.26827 6.84809 7.24878 6.74937 7.24878C6.65066 7.24878 6.55292 7.26827 6.46176 7.30612C6.3706 7.34398 6.2878 7.39946 6.21812 7.46938L4.71812 8.96937C4.6482 9.03905 4.59273 9.12185 4.55487 9.21301C4.51702 9.30417 4.49753 9.40191 4.49753 9.50062C4.49753 9.59933 4.51702 9.69707 4.55487 9.78824C4.59273 9.8794 4.6482 9.9622 4.71812 10.0319L6.21812 11.5319C6.35902 11.6728 6.55012 11.7519 6.74937 11.7519C6.94863 11.7519 7.13973 11.6728 7.28062 11.5319C7.42152 11.391 7.50068 11.1999 7.50068 11.0006C7.50068 10.8014 7.42152 10.6103 7.28062 10.4694L6.3125 9.5L7.2825 8.53063C7.35211 8.46085 7.40728 8.37803 7.44487 8.28692C7.48245 8.1958 7.5017 8.09817 7.50153 7.99961C7.50135 7.90105 7.48175 7.80349 7.44385 7.71251C7.40595 7.62152 7.35048 7.53891 7.28062 7.46938ZM13.75 5.5V13.5C13.75 13.8315 13.6183 14.1495 13.3839 14.3839C13.1495 14.6183 12.8315 14.75 12.5 14.75H3.5C3.16848 14.75 2.85054 14.6183 2.61612 14.3839C2.3817 14.1495 2.25 13.8315 2.25 13.5V2.5C2.25 2.16848 2.3817 1.85054 2.61612 1.61612C2.85054 1.3817 3.16848 1.25 3.5 1.25H9.5C9.59856 1.25 9.69616 1.26944 9.78721 1.30719C9.87826 1.34494 9.96097 1.40026 10.0306 1.47L13.5306 4.97C13.6711 5.11062 13.75 5.30124 13.75 5.5ZM10 3.5625V5H11.4375L10 3.5625ZM12.25 13.25V6.5H9.25C9.05109 6.5 8.86032 6.42098 8.71967 6.28033C8.57902 6.13968 8.5 5.94891 8.5 5.75V2.75H3.75V13.25H12.25Z"
          fill={iconColor}
        />
      </svg>
    );
  }
  else if (type === SharpCommand.CODEBASE) {
    return (
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4.47996 6.07641L2.17121 8.00016L4.47996 9.92391C4.55653 9.98666 4.61992 10.0639 4.66647 10.1513C4.71302 10.2387 4.74182 10.3344 4.7512 10.433C4.76057 10.5315 4.75035 10.6309 4.72111 10.7255C4.69187 10.8201 4.6442 10.9079 4.58084 10.984C4.51748 11.0601 4.43969 11.1228 4.35195 11.1687C4.26421 11.2146 4.16827 11.2426 4.06964 11.2512C3.97102 11.2598 3.87167 11.2487 3.77733 11.2187C3.68299 11.1888 3.59552 11.1404 3.51996 11.0764L0.519963 8.57641C0.43547 8.50603 0.367489 8.41792 0.320836 8.31834C0.274183 8.21876 0.25 8.11013 0.25 8.00016C0.25 7.89019 0.274183 7.78157 0.320836 7.68199C0.367489 7.5824 0.43547 7.4943 0.519963 7.42391L3.51996 4.92391C3.67301 4.79849 3.86942 4.73863 4.0664 4.75737C4.26339 4.77612 4.44498 4.87195 4.57162 5.02398C4.69827 5.17602 4.7597 5.37195 4.74253 5.56907C4.72536 5.7662 4.63098 5.94856 4.47996 6.07641ZM15.48 7.42391L12.48 4.92391C12.4044 4.85995 12.3169 4.81157 12.2226 4.78158C12.1283 4.75158 12.0289 4.74056 11.9303 4.74915C11.8317 4.75774 11.7357 4.78577 11.648 4.83162C11.5602 4.87748 11.4824 4.94025 11.4191 5.01631C11.3557 5.09237 11.3081 5.18023 11.2788 5.27481C11.2496 5.36939 11.2394 5.46882 11.2487 5.56737C11.2581 5.66592 11.2869 5.76164 11.3335 5.84901C11.38 5.93638 11.4434 6.01366 11.52 6.07641L13.8287 8.00016L11.52 9.92391C11.4434 9.98666 11.38 10.0639 11.3335 10.1513C11.2869 10.2387 11.2581 10.3344 11.2487 10.433C11.2394 10.5315 11.2496 10.6309 11.2788 10.7255C11.3081 10.8201 11.3557 10.9079 11.4191 10.984C11.4824 11.0601 11.5602 11.1228 11.648 11.1687C11.7357 11.2146 11.8317 11.2426 11.9303 11.2512C12.0289 11.2598 12.1283 11.2487 12.2226 11.2187C12.3169 11.1888 12.4044 11.1404 12.48 11.0764L15.48 8.57641C15.5645 8.50603 15.6324 8.41792 15.6791 8.31834C15.7257 8.21876 15.7499 8.11013 15.7499 8.00016C15.7499 7.89019 15.7257 7.78157 15.6791 7.68199C15.6324 7.5824 15.5645 7.4943 15.48 7.42391ZM10.2562 1.79516C10.1636 1.76149 10.0653 1.74639 9.96691 1.75073C9.86849 1.75506 9.77189 1.77874 9.68263 1.82041C9.59336 1.86208 9.51318 1.92093 9.44667 1.9936C9.38015 2.06626 9.3286 2.15132 9.29496 2.24391L5.29496 13.2439C5.22716 13.4308 5.23634 13.637 5.32049 13.8171C5.40464 13.9972 5.55686 14.1366 5.74371 14.2045C5.82575 14.2348 5.91252 14.2503 5.99996 14.2502C6.15395 14.2502 6.30421 14.2028 6.43034 14.1145C6.55647 14.0261 6.65235 13.9011 6.70496 13.7564L10.705 2.75641C10.7386 2.66383 10.7537 2.56552 10.7494 2.46711C10.7451 2.36869 10.7214 2.27209 10.6797 2.18283C10.638 2.09356 10.5792 2.01338 10.5065 1.94686C10.4339 1.88035 10.3488 1.8288 10.2562 1.79516Z"
          fill={iconColor}
        />
      </svg>
    );
  }
  else if (type === SharpCommand.FOLDER) {
    return (
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4.75 4C4.75 3.80109 4.82902 3.61032 4.96967 3.46967C5.11032 3.32902 5.30109 3.25 5.5 3.25H13.5C13.6989 3.25 13.8897 3.32902 14.0303 3.46967C14.171 3.61032 14.25 3.80109 14.25 4C14.25 4.19891 14.171 4.38968 14.0303 4.53033C13.8897 4.67098 13.6989 4.75 13.5 4.75H5.5C5.30109 4.75 5.11032 4.67098 4.96967 4.53033C4.82902 4.38968 4.75 4.19891 4.75 4ZM13.5 7.25H5.5C5.30109 7.25 5.11032 7.32902 4.96967 7.46967C4.82902 7.61032 4.75 7.80109 4.75 8C4.75 8.19891 4.82902 8.38968 4.96967 8.53033C5.11032 8.67098 5.30109 8.75 5.5 8.75H13.5C13.6989 8.75 13.8897 8.67098 14.0303 8.53033C14.171 8.38968 14.25 8.19891 14.25 8C14.25 7.80109 14.171 7.61032 14.0303 7.46967C13.8897 7.32902 13.6989 7.25 13.5 7.25ZM13.5 11.25H5.5C5.30109 11.25 5.11032 11.329 4.96967 11.4697C4.82902 11.6103 4.75 11.8011 4.75 12C4.75 12.1989 4.82902 12.3897 4.96967 12.5303C5.11032 12.671 5.30109 12.75 5.5 12.75H13.5C13.6989 12.75 13.8897 12.671 14.0303 12.5303C14.171 12.3897 14.25 12.1989 14.25 12C14.25 11.8011 14.171 11.6103 14.0303 11.4697C13.8897 11.329 13.6989 11.25 13.5 11.25ZM2.75 7C2.55222 7 2.35888 7.05865 2.19443 7.16853C2.02998 7.27841 1.90181 7.43459 1.82612 7.61732C1.75043 7.80004 1.73063 8.00111 1.76922 8.19509C1.8078 8.38907 1.90304 8.56725 2.04289 8.70711C2.18275 8.84696 2.36093 8.9422 2.55491 8.98079C2.74889 9.01937 2.94996 8.99957 3.13268 8.92388C3.31541 8.84819 3.47159 8.72002 3.58147 8.55557C3.69135 8.39112 3.75 8.19778 3.75 8C3.75 7.73478 3.64464 7.48043 3.45711 7.29289C3.26957 7.10536 3.01522 7 2.75 7ZM2.75 3C2.55222 3 2.35888 3.05865 2.19443 3.16853C2.02998 3.27841 1.90181 3.43459 1.82612 3.61732C1.75043 3.80004 1.73063 4.00111 1.76922 4.19509C1.8078 4.38907 1.90304 4.56725 2.04289 4.70711C2.18275 4.84696 2.36093 4.9422 2.55491 4.98079C2.74889 5.01937 2.94996 4.99957 3.13268 4.92388C3.31541 4.84819 3.47159 4.72002 3.58147 4.55557C3.69135 4.39112 3.75 4.19778 3.75 4C3.75 3.73478 3.64464 3.48043 3.45711 3.29289C3.26957 3.10536 3.01522 3 2.75 3ZM2.75 11C2.55222 11 2.35888 11.0586 2.19443 11.1685C2.02998 11.2784 1.90181 11.4346 1.82612 11.6173C1.75043 11.8 1.73063 12.0011 1.76922 12.1951C1.8078 12.3891 1.90304 12.5673 2.04289 12.7071C2.18275 12.847 2.36093 12.9422 2.55491 12.9808C2.74889 13.0194 2.94996 12.9996 3.13268 12.9239C3.31541 12.8482 3.47159 12.72 3.58147 12.5556C3.69135 12.3911 3.75 12.1978 3.75 12C3.75 11.7348 3.64464 11.4804 3.45711 11.2929C3.26957 11.1054 3.01522 11 2.75 11Z"
          fill={iconColor}
        />
      </svg>
    );
  }
  else if (type === SharpCommand.FILE) {
    return (
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13.5306 4.96997L10.0306 1.46997C9.96097 1.40023 9.87826 1.34491 9.78721 1.30716C9.69616 1.26941 9.59856 1.24997 9.5 1.24997H3.5C3.16848 1.24997 2.85054 1.38167 2.61612 1.61609C2.3817 1.85051 2.25 2.16845 2.25 2.49997V13.5C2.25 13.8315 2.3817 14.1494 2.61612 14.3839C2.85054 14.6183 3.16848 14.75 3.5 14.75H12.5C12.8315 14.75 13.1495 14.6183 13.3839 14.3839C13.6183 14.1494 13.75 13.8315 13.75 13.5V5.49997C13.75 5.30121 13.6711 5.11059 13.5306 4.96997ZM10 3.56247L11.4375 4.99997H10V3.56247ZM3.75 13.25V2.74997H8.5V5.74997C8.5 5.94888 8.57902 6.13965 8.71967 6.2803C8.86032 6.42095 9.05109 6.49997 9.25 6.49997H12.25V13.25H3.75ZM10.75 8.24997C10.75 8.44888 10.671 8.63965 10.5303 8.7803C10.3897 8.92095 10.1989 8.99997 10 8.99997H6C5.80109 8.99997 5.61032 8.92095 5.46967 8.7803C5.32902 8.63965 5.25 8.44888 5.25 8.24997C5.25 8.05106 5.32902 7.86029 5.46967 7.71964C5.61032 7.57899 5.80109 7.49997 6 7.49997H10C10.1989 7.49997 10.3897 7.57899 10.5303 7.71964C10.671 7.86029 10.75 8.05106 10.75 8.24997ZM10.75 10.75C10.75 10.9489 10.671 11.1396 10.5303 11.2803C10.3897 11.421 10.1989 11.5 10 11.5H6C5.80109 11.5 5.61032 11.421 5.46967 11.2803C5.32902 11.1396 5.25 10.9489 5.25 10.75C5.25 10.5511 5.32902 10.3603 5.46967 10.2196C5.61032 10.079 5.80109 9.99997 6 9.99997H10C10.1989 9.99997 10.3897 10.079 10.5303 10.2196C10.671 10.3603 10.75 10.5511 10.75 10.75Z"
          fill={iconColor}
        />
      </svg>
    );
  }
  else if (type === SharpCommand.RULES) {
    return <Icon icon="lucide:file-code" className="size-4" />;
  }
  return null;
};
