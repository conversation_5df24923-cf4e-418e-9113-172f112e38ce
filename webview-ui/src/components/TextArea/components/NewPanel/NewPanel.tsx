import { CustomScrollBar } from "@/components/CustomScrollbar";
import { useRichEditPanelMenuStore } from "@/store/richEditorPanelMenu";
import clsx from "clsx";
import { createContext, forwardRef, ReactNode, RefObject, useCallback, useContext, useEffect, useMemo, useRef, useState } from "react";

import { RichEditorBoxPanelData, RichEditorMenuType } from "../../const";
import { $insertBlock, registerArrowCommands, triggerEnter } from "../../utils";
import { throwNeverError } from "@/utils/throwUnknownError";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { $getRoot, $getSelection, $insertNodes, LexicalNode } from "lexical";
import { $createMentionNode } from "../../lexical/CommandNode";
import { useColorMode, useControllableState, useMergeRefs } from "@chakra-ui/react";
import { createPortal } from "react-dom";
import { useRichEditorContext } from "../../hooks/useRichEditorContext";
import KidIcon from "@kid/enterprise-icon/icon/output/Icon";
import IconArrowLeft from "@kid/enterprise-icon/icon/output/kwaipilot/system/kwaipilot_system_smallarrow_left";
import { CUSTOM_PROMPT_POPOVER_CONTENT_CLASS_NAME, MenuList } from "./MenuList";
import { CollectionContext, useCollection } from "./collection";
import { useSelectableMenuItemSequence } from "./useSelectableMenuItemSequence";
import { CustomPromptData } from "shared/lib/CustomVariable";
import { $compileLexicalNodes } from "../../CustomVariable/renderLexicalNodes";
import repoChatService from "@/services/repo-chat";
import { useAsync, useLatest } from "react-use";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { firstValueFrom } from "rxjs";
import { SerializedCustomPromptData, useRecentUsedPrompts } from "./useRecentUsedPrompts";
import { reportUserAction, weblog } from "@/utils/weblogger";
import { SharpCommand } from "@shared/types";
import { URI } from "vscode-uri";
import { Icon } from "@iconify/react";
import { ReportOpt } from "@shared/types/logger";

export interface PanelHeaderData {
  title: string | JSX.Element;
  description: string;
  icon: ReactNode;
}
export type NewPanelFirstStageMenuType = RichEditorMenuType.SHARP_COMMAND | RichEditorMenuType.SLASH_COMMAND;

/**
 * MenuEnum 和 NewPanelFirstStageMenuType 区别在于， NewPanelFirstStageMenuType只是第一层的菜单，
 * 而 MenuEnum 是所有菜单的枚举，用于保存菜单的当前 selectIndex 等状态
 */
// export enum NewPanelMenuEnum {
//   /* share_command 一级菜单 */
//   SHARP_COMMAND = "SHARP_COMMAND",
//   /* slash 一级菜单 */
//   SLASH_COMMAND = "SLASH_COMMAND",
//   /* 自定义指令列表 */
//   CUSTOM_PROMPT = "CUSTOM_PROMPT",
//   /* 文件列表 */
//   FILE_LIST = "FILE_LIST",
// }

export function NewPanelProvider(props: { children: ReactNode }) {
  return (
    <NewPanelContext.Provider value={null}>
      {props.children}
    </NewPanelContext.Provider>
  );
}

export interface NewPanelProps {
  menuType: NewPanelFirstStageMenuType;
  query: string;
  panelHeaderData: null | PanelHeaderData;
  subMenuState?: RichEditorBoxPanelData | null;
  onSubMenuStateChange?: (subMenuState: RichEditorBoxPanelData | null) => void;
  panelRef: RefObject<HTMLDivElement>;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  filterSharpCommandKeyList?: SharpCommand[];
}

type NewPanelContextState = Required<NewPanelProps> & {
  selectIndex: number | "header";
  /* 有些选项需要等待异步操作完成， 此时需要将 index 记录下来 */
  submittingIndex: number | null;
  onSubmittingIndexChange: (submittingIndex: number | null) => void;
  onSelectIndexChange: (selectIndex: number | "header") => void;
  handleSelectMenu: (menu: RichEditorBoxPanelData, index: number) => void;
  /** 是否处在键盘事件的 delay 中， 如果处在 delay 中， 则不触发 hover 事件 */
  isKeyboardEvent: boolean;
  onIsKeyboardEventChange: (isKeyboardEvent: boolean) => void;
  reportCustomPromptRecentUsed: (data: CustomPromptData) => void;
  recentUsedPrompts: SerializedCustomPromptData[];
};

export const NewPanelContext = createContext<NewPanelContextState | null>(null);

export function useNewPanelContext() {
  const context = useContext(NewPanelContext);
  if (!context) {
    throw new Error("NewPanelContext is not provided");
  }
  return context;
}

function getAncestorElement(element: HTMLElement): HTMLElement[] {
  const result: HTMLElement[] = [];
  while (element.parentElement) {
    element = element.parentElement;
    result.push(element);
  }
  return result;
}

export const _NewPanel = forwardRef<HTMLDivElement>((_, forwardedRef) => {
  const contextState = useContext(NewPanelContext);
  const { getItems } = useCollection();

  if (!contextState) {
    throw new Error("NewPanelContext is not provided");
  }

  const {
    panelHeaderData,
    panelRef,
    open,
    onOpenChange: setOpen,
    subMenuState: submenuState,
    onSubMenuStateChange: setSubmenuState,
    selectIndex,
    onSelectIndexChange: setSelectIndex,
    handleSelectMenu,
    isKeyboardEvent,
    onIsKeyboardEventChange: setIsKeyboardEvent,
  } = contextState;

  const [editor] = useLexicalComposerContext();

  const panelMenuStore = useRichEditPanelMenuStore();

  const { focused } = useRichEditorContext();

  const rootFocusableRef = useRef<HTMLDivElement>(null);

  const [isPanelFocused, setIsPanelFocused] = useState(false);

  const composedRef = useMergeRefs(rootFocusableRef, forwardedRef);

  useEffect(() => {
    if (!focused) {
      // 延迟 确保 activeElement 更新
      setTimeout(() => {
        const isFocusedWithin = rootFocusableRef.current?.contains(document.activeElement)
          || getAncestorElement(document.activeElement as HTMLElement)
            .some(element => element.classList.contains(CUSTOM_PROMPT_POPOVER_CONTENT_CLASS_NAME));
        if (!isFocusedWithin) {
          setOpen(false);
        }
      }, 0);
    }
  }, [focused, setOpen]);

  const latestFocused = useLatest(focused);
  useEffect(() => {
    // 从 panel 失焦，但最新焦点也没有在编辑器，则关闭。需要延时获取确保新的 activeElement 更新
    if (!isPanelFocused) {
      setTimeout(() => {
        if (!latestFocused.current) {
          setOpen(false);
        }
      }, 0);
    }
  }, [isPanelFocused, setOpen, latestFocused]);

  const disableMenu = useMemo(() => {
    return panelMenuStore.disabledMenu;
  }, [panelMenuStore.disabledMenu]);

  useEffect(() => {
    if (selectIndex === "header") {
      return;
    }
    const items = getItems();
    if (items[selectIndex]) {
      items[selectIndex].ref.current?.scrollIntoView({
        block: "nearest",
      });
    }
  }, [selectIndex, getItems]);

  const headerInteractable = Boolean(submenuState);

  const {
    getPrev, getNext,
  } = useSelectableMenuItemSequence({
    includeHeader: headerInteractable,
  });

  /** 重置当前选中的item */
  useEffect(() => {
    if (open && selectIndex === -1) {
      const idx = getNext(
        -1,
      );
      setSelectIndex(idx);
    }
  }, [getNext, setSelectIndex, open, selectIndex]);

  useEffect(() => {
    const disposeArrowCommands = registerArrowCommands(
      editor,
      open,
      setIsKeyboardEvent,
      {
        onMoveNext: () => {
          const idx = getNext(selectIndex);
          setSelectIndex(idx);
        },
        onMovePrev: () => {
          const idx = getPrev(selectIndex);
          setSelectIndex(idx);
        },
      },
    );
    return () => {
      disposeArrowCommands();
    };
  }, [editor, selectIndex, panelMenuStore.disabledMenu, open, getNext, getPrev, setIsKeyboardEvent, setSelectIndex]);

  useEffect(() => {
    const select = () => {
      if (selectIndex === "header") {
        // 返回
        setSubmenuState(null);
        return;
      }
      const items = getItems();
      if (items[selectIndex]) {
        handleSelectMenu(items[selectIndex].data, selectIndex);
      }
    };
    const dispose = triggerEnter(editor, open, select);

    return () => {
      dispose();
    };
  }, [editor, handleSelectMenu, selectIndex, open, setSubmenuState, getItems]);

  const handleRuleCreateClick = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    e.stopPropagation();
    e.preventDefault();
    kwaiPilotBridgeAPI.extensionComposer.$addRuleFile();
    const param: ReportOpt<"input_rules_create"> = {
      key: "input_rules_create",
      type: undefined,
      content: "common_chat",
    };
    reportUserAction(param);
  }, []);

  const submenuTitleBox = useMemo(() => {
    if (!panelHeaderData) {
      return null;
    }
    return (
      <div className=" p-[3px] border-b-[0.5px] border-border-horizontal">
        <button
          className={clsx(
            "flex px-3 w-full  items-center   h-[36px] box-border rounded justify-between  leading-[19.5px]",
            selectIndex === "header" ? "bg-bg-hover" : "bg-bg-fill",
          )}
          onMouseEnter={() => {
            if (!isKeyboardEvent && headerInteractable) {
              setSelectIndex("header");
            }
          }}
          disabled={!headerInteractable}
          onClick={() => {
            setSubmenuState(null);
          }}
        >
          <div
            className={clsx("truncate", [
              submenuState?.key && disableMenu[submenuState.key]?.status
                ? "text-text-common-disable"
                : "text-text-common-primary",
            ])}
          >
            {submenuState
              ? (
                  <div className={clsx(
                    "flex gap-[6px] items-center font-medium leading-[19.5px]",
                    selectIndex === "header" ? "bg-bg-hover" : "bg-bg-fill",
                  )}
                  >
                    <KidIcon config={IconArrowLeft} size={16} color="currentColor"></KidIcon>
                    {panelHeaderData.icon}
                    {panelHeaderData.title}
                  </div>
                )
              : (
                  <div className="flex gap-[6px] items-center font-medium leading-[19.5px]">
                    {panelHeaderData.icon}
                    {panelHeaderData.title}
                  </div>
                )}
          </div>
          <div
            className={clsx("truncate", [
              submenuState?.key && disableMenu[submenuState.key]?.status
                ? "text-text-common-disable"
                : "text-text-common-secondary",
            ])}
          >
            {panelHeaderData.title === "规则"
              ? (
                  <div
                    onClick={handleRuleCreateClick}
                    className=" text-[12px] text-text-common-primary ml-auto flex items-center gap-1"
                  >
                    <Icon icon="famicons:add" />
                    添加项目规则
                  </div>
                )
              : panelHeaderData.description}
          </div>
        </button>
      </div>
    );
  }, [panelHeaderData, selectIndex, headerInteractable, submenuState, disableMenu, handleRuleCreateClick, isKeyboardEvent, setSelectIndex, setSubmenuState]);

  return panelRef.current
    && createPortal((

      <div
        ref={composedRef}
        tabIndex={-1}
        onFocus={() => setIsPanelFocused(true)}
        onBlur={() => setIsPanelFocused(false)}
        onMouseDown={e => (e.target as HTMLElement).focus()}
        className={clsx(
          `absolute top-0 left-0 bg-bg-fill text-[13px] leading-[18px] overflow-hidden rounded-[6px] border border-border-common `,
          open ? undefined : "hidden",
        )}
        style={{
          transform: "translate(-1px, calc(-100% - 6px))",
          width: "calc(100% + 2px)",
        }}
      >
        {submenuTitleBox}
        <CustomScrollBar suppressScrollX className="max-h-[300px]">
          <div className="w-full px-1 py-1">
            <MenuList>
            </MenuList>
          </div>
        </CustomScrollBar>
      </div>
    ), panelRef.current);
});

export const NewPanel = forwardRef<HTMLElement, NewPanelProps>((props, forwardedRef) => {
  const {
    query,
    menuType,
    panelHeaderData,
    subMenuState: subMenuStateProp,
    onSubMenuStateChange,
    panelRef,
    open: openProp,
    onOpenChange,
    filterSharpCommandKeyList,
  } = props;

  const itemMap = useRef(new Map<
    RefObject<HTMLElement | null>,
    { ref: RefObject<HTMLElement | null>; data: RichEditorBoxPanelData } & { disabled: boolean }
  >());

  const [open = false, setOpen] = useControllableState({
    value: openProp,
    defaultValue: false,
    onChange: onOpenChange,
  });

  const [editor] = useLexicalComposerContext();

  const [submenuState, setSubmenuState] = useSubmenuState({
    open,
    subMenuStateProp,
    onSubMenuStateChange,
  });

  const [selectIndex, setSelectIndex] = useState<number | /* 表明选中的是标题区域, 点击后会返回 */"header">(-1);

  const [submittingIndex, setSubmittingIndex] = useState<number | null>(null);

  const isDark = useColorMode().colorMode === "dark";
  const insertBlock = useCallback(
    (menu: RichEditorBoxPanelData) => {
      editor.update(() => {
        const selection = $getSelection();
        if (menuType === RichEditorMenuType.SHARP_COMMAND) {
          $insertBlock($createMentionNode(
            `#${menu.title}`,
            menu,
            isDark,
          ), selection, ["#"]);
        }
        else if (menuType === RichEditorMenuType.SLASH_COMMAND) {
          $insertBlock($createMentionNode(
            `/${menu.title}`,
            menu,
            isDark,
          ), selection, ["/", "、"]);
        }
        else {
          throwNeverError(menuType);
        }
      });
    },
    [editor, isDark, menuType],
  );

  const repoPath = useAsync(() => repoChatService.getWorkspacePathAndRepoPath().then(res => res.repoPath), []);

  const { reportCustomPromptRecentUsed, recentUsedPrompts } = useRecentUsedPrompts();

  const handleSelectMenu = useCallback(
    (menu: RichEditorBoxPanelData, index: number) => {
      if (menu.type === "submenu") {
        setSubmenuState(menu);
      }
      else if (menu.type === "normal") {
        /** 选中这一项 */
        insertBlock(menu);
        setSubmenuState(null);
      }
      else if (menu.type === "customPrompt") {
        return (async () => {
          setSubmittingIndex(index);
          reportCustomPromptRecentUsed(menu.raw);
          weblog?.sendImmediately("CLICK", {
            action: "VS_CUSTOM_PROMPT_ITEM",
            params: {
              id: menu.raw.id,
              name: menu.raw.name,
            },
            type: "USER_OPERATION",
          });
          const currentFile = await firstValueFrom(kwaiPilotBridgeAPI.observableAPI.currentFileAndSelection());
          const language = (await kwaiPilotBridgeAPI.getActiveEditor()).document.language;
          let updateFailed = false;
          editor.update(() => {
            let nodes: LexicalNode[] = [];
            try {
              nodes = $compileLexicalNodes(menu.raw.content, {
                repoPath: repoPath.value,
                repoName: repoChatService.repoName,
                currentFile: currentFile
                  ? {
                      type: "file",
                      ...currentFile,
                      uri: URI.parse(currentFile.uri),
                    }
                  : null,
                language,
              });
            }
            catch (e) {
              kwaiPilotBridgeAPI.showToast({
                message: `解析 prompt 模板失败，请尝试联系prompt负责人，错误信息:${e instanceof Error ? e.message : String(e) || "unknown"}`,
                level: "error",
              });
              updateFailed = true;
              return;
            }
            const root = $getRoot();
            root.clear();
            $insertNodes(nodes);
          });
          if (updateFailed) {
            setSubmittingIndex(null);
            return;
          }
          setSubmenuState(null);
          setSubmittingIndex(null);
        })();
      }
      else {
        throwNeverError(menu);
      }
    },
    [setSubmenuState, insertBlock, reportCustomPromptRecentUsed, editor, repoPath.value],
  );
  const [isKeyboardEvent, setIsKeyboardEvent] = useState(false);

  const contextState = useMemo<Required<NewPanelContextState>>(() => ({
    query,
    menuType,
    panelHeaderData,
    subMenuState: submenuState,
    onSubMenuStateChange: setSubmenuState,
    open,
    onOpenChange: setOpen,
    panelRef,
    selectIndex,
    onSelectIndexChange: setSelectIndex,
    handleSelectMenu,
    isKeyboardEvent,
    onIsKeyboardEventChange: setIsKeyboardEvent,
    submittingIndex,
    onSubmittingIndexChange: setSubmittingIndex,
    reportCustomPromptRecentUsed, recentUsedPrompts,
    filterSharpCommandKeyList: filterSharpCommandKeyList ?? [],
  }), [query, menuType, panelHeaderData, submenuState, setSubmenuState, open, setOpen, panelRef, selectIndex, handleSelectMenu, isKeyboardEvent, submittingIndex, reportCustomPromptRecentUsed, recentUsedPrompts, filterSharpCommandKeyList]);

  const collectionRef = useRef<HTMLElement | null>(null);

  const composedRef = useMergeRefs(collectionRef, forwardedRef);

  return (
    <NewPanelContext.Provider value={contextState}>
      <CollectionContext.Provider value={{ itemMap: itemMap.current, collectionRef: collectionRef }}>
        <_NewPanel {...props} ref={composedRef} />
      </CollectionContext.Provider>
    </NewPanelContext.Provider>
  );
});

/**
 * 当前是否在子菜单中
 * @param props
 * @returns
 */
function useSubmenuState({
  open,
  subMenuStateProp,
  onSubMenuStateChange,
}: {

  open: boolean;
  subMenuStateProp?: RichEditorBoxPanelData | null;
  onSubMenuStateChange?: (subMenuState: RichEditorBoxPanelData | null) => void;
}) {
  const [submenuState, setSubmenuState] = useControllableState<RichEditorBoxPanelData | null>({
    value: subMenuStateProp,
    defaultValue: null,
    onChange: onSubMenuStateChange,
  }) as [RichEditorBoxPanelData | null, React.Dispatch<React.SetStateAction<RichEditorBoxPanelData | null>>];
  /* 不能将 submenuState 设置为 undefined 否则会导致 useControllableState内部呗判断呈 unControlled */

  useEffect(() => {
    if (!open) {
      setSubmenuState(null);
    }
  }, [open, setSubmenuState]);

  return [submenuState, setSubmenuState] as const;
}
