import { CustomScrollBar } from "@/components/CustomScrollbar";
import { useRecordStore } from "@/store/record";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  Tooltip,
  useDisclosure,
} from "@chakra-ui/react";
import { clsx } from "clsx";
import { useMemo } from "react";
import ClearKnowledgeIcon from "@/assets/icons/knowledge-clear.svg?react";
import ArrowDownIcon from "@/assets/icons/arrow-up.svg?react";
import KnowledgeDisableIcon from "@/assets/icons/knowledge-disable.svg?react";
import KnowledgeSelectIcon from "@/assets/icons/knowledge-select.svg?react";
import css from "./modelArea.module.less";
import { Doc } from "@shared/types/business";

export const KnowledgeSelector: React.FC = () => {
  const docList = useRecordStore(state => state.docList);
  const { onOpen, onClose, isOpen, onToggle } = useDisclosure();
  const docId = useRecordStore(state => state.docId);
  const setDocId = useRecordStore(state => state.setDocId);

  const doc = docList.find(item => item.id === docId);

  const selectKnowledge = (doc: Doc) => {
    const params: ReportOpt<"chSetting"> = {
      key: "chSetting",
      type: "knRepo",
      content: doc.id.toString(),
    };
    reportUserAction(params);
    const id = Number(doc.id);
    setDocId(id);
    onClose();
  };

  const clearKnowledge = () => {
    setDocId(0);
    onClose();
  };

  const clearDoc = () => {
    setDocId(0);
  };

  const KnowledgeTrigger = useMemo(() => {
    const doc = docList.find(item => item.id === docId);
    const name = doc?.name || "知识库";

    return (
      <div
        className={clsx(
          "hover:bg-bg-controls-hover group flex items-center gap-1 cursor-pointer rounded px-1.5 py-1 hover:",
          {
            "bg-bg-controls-hover": isOpen,
            "bg-bg-brand-active": doc,
          },
        )}
        onMouseDown={e => e.preventDefault()}
        onClick={onToggle}
      >
        {!isOpen
          ? (
              <div className="w-4 h-4 flex items-center justify-center text-icon-common-secondary">
                {doc
                  ? (
                      <KnowledgeSelectIcon className="group-hover:hidden text-text-brand-default hover:"></KnowledgeSelectIcon>
                    )
                  : (
                      <KnowledgeDisableIcon className="group-hover:hidden "></KnowledgeDisableIcon>
                    )}
                <ArrowDownIcon
                  className={clsx("hidden group-hover:block rotate-180", {
                    "group-hover:text-text-brand-default": doc,
                  })}
                >
                </ArrowDownIcon>
              </div>
            )
          : (
              <div
                className={clsx(
                  "w-4 h-4 flex items-center justify-center transition-all",
                  {
                    "rotate-180": !isOpen,
                  },
                  [doc ? "text-text-brand-default" : " text-text-common-secondary"],
                )}
              >
                <ArrowDownIcon></ArrowDownIcon>
              </div>
            )}
        <span
          className={clsx(
            // !showText && css["model-area-item-text-hidden"],
            [doc ? "text-text-brand-default" : "text-text-common-secondary"],
            "text-[13px]",
            css["model-area-item-text-hidden"],
            "truncate",
          )}
        >
          {name}
        </span>
      </div>
    );
  }, [docId, docList, isOpen, onToggle]);

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      strategy="fixed"
      placement="top-start"
    >
      <PopoverTrigger>
        <div>
          <Tooltip label={docId ? `已关联{${doc?.name}}` : "关联知识库"}>
            {KnowledgeTrigger}
          </Tooltip>
        </div>
      </PopoverTrigger>
      <PopoverContent
        border="none"
        w="200px"
        sx={{
          "&:focus": {
            outline: "none",
          },
          "&:focus-visible": {
            outline: "none",
            boxShadow: "none",
          },
        }}
      >
        <div className="flex flex-col bg-bg-fill border border-border-common rounded">
          <CustomScrollBar suppressScrollX className="h-[224px] w-[200px]">
            <div className="p-1 w-full flex flex-col gap-1 rounded bg-bg-fill">
              {docList.map((doc, idx) => {
                return (
                  <div
                    className={clsx(
                      "text-text-common-primary leading-[19.5px] text-[13px] w-full rounded-sm px-3 py-2 hover:bg-bg-hover cursor-pointer",
                      {
                        "bg-bg-selected": docId === doc.id,
                      },
                    )}
                    onClick={() => {
                      selectKnowledge(doc);
                    }}
                    key={idx}
                  >
                    {doc.name}
                  </div>
                );
              })}
            </div>
          </CustomScrollBar>
          <div
            className="flex gap-1 h-9 items-center px-3 border-t border-border-common"
            onClick={clearKnowledge}
          >
            <div className="w-4 h-4 flex justify-center items-center text-text-brand-default">
              <ClearKnowledgeIcon></ClearKnowledgeIcon>
            </div>
            <span
              className="text-[13px] leading-[19.5px] text-text-brand-default cursor-pointer"
              onClick={clearDoc}
            >
              清除已选项
            </span>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
