import { ModelSelector } from "./ModelSelector";
import { Divider } from "./Divider";
import { KnowledgeSelector } from "./KnowledgeSelector";
import { LinkNet } from "./LinkNet";
import { createContext } from "react";
import { RepoSelector } from "./RepoSelector";
import baseInfoManager from "@/utils/baseInfo";
import { SerializedEditorState } from "lexical";

export const ShowRightTextContext = createContext(false);

export const ModelArea: React.FC<{ richEditorState: SerializedEditorState | undefined }> = ({ richEditorState }) => {
  return (
    <div className="w-full flex justify-between gap-6">
      <div className="flex gap-1 items-center overflow-hidden">
        <ModelSelector placement="top-start" className="py-1 px-1.5 " />
        <Divider />
        {baseInfoManager.isXcode && (
          <>
            <RepoSelector />
            <Divider />
          </>
        )}
        <LinkNet richEditorState={richEditorState} />
        <Divider />
        <KnowledgeSelector />
      </div>
    </div>
  );
};
