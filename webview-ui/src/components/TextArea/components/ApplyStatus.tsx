import { kwaiPilotBridgeAPI } from "@/bridge";
import {
  FileStateUIType,
  useComposerStatusStore,
  useGlobalComposerState,
} from "@/store/composerStatus";
import { useComposerAffectedFileList, useRecordStore } from "@/store/record";
import { FileStateType } from "@shared/types/chatHistory";
import { produce } from "immer";
import { useCallback, useMemo } from "react";

const STATE_LABEL_MAP: Record<FileStateUIType, string> = {
  accepted: "已接受",
  applied: "以应用",
  applying: "应用中...",
  init: "",
  rejected: "已拒绝",
};

const ApplyStatus = () => {
  const { fileList } = useComposerAffectedFileList();
  const { state } = useGlobalComposerState();
  const activeSession = useRecordStore(s => s.activeSession);
  const sessionHistory = useRecordStore(s => s.sessionHistory);
  const fileStateMap = useComposerStatusStore(state => state.fileStateMap);
  const updateFileState = useComposerStatusStore(
    state => state.updateFileState,
  );

  const canAcceptOrReject = useMemo<boolean>(
    () =>
      Object.values(fileStateMap)
        .map(v => Object.values(v))
        .flat()
        .some(file => file.state === "applied"),
    [fileStateMap],
  );

  const isGenerating = useRecordStore(
    state => state.loadingStatu?.status === "loading",
  );

  const updateSqliteState = useCallback(
    (state: Exclude<FileStateType, "init">) => {
      // 找出所有受影响的 qaItem 中的 file
      for (const chatId in fileStateMap) {
        const targetQAItem = sessionHistory?.cachedMessages.find(
          v => v.id === chatId,
        );
        if (!targetQAItem) {
          continue;
        }

        const modifiedQAItem = produce(targetQAItem, (draft) => {
          const latestAnswer = draft?.A.at(-1);
          if (!latestAnswer) {
            return;
          }
          latestAnswer.affectedFileState ||= {};
          for (const filepath in fileStateMap[chatId]) {
            const file = fileStateMap[chatId][filepath];
            if (file.state === "applied") {
              latestAnswer.affectedFileState[filepath] = {
                state,
              };
            }
          }
        });

        kwaiPilotBridgeAPI.addMessage({
          item: modifiedQAItem,
          sessionId: activeSession,
          chatId,
        });
      }
    },
    [activeSession, fileStateMap, sessionHistory?.cachedMessages],
  );

  const handleAcceptAll = useCallback(() => {
    kwaiPilotBridgeAPI.editor.acceptAllFileDiff();

    // 找出所有受影响的 qaItem 中的 file
    for (const chatId in fileStateMap) {
      for (const filepath in fileStateMap[chatId]) {
        const file = fileStateMap[chatId][filepath];
        if (file.state === "applied") {
          updateFileState(chatId, filepath, "accepted");
        }
      }
    }
    updateSqliteState("accepted");
  }, [fileStateMap, updateFileState, updateSqliteState]);
  const handleRejectAll = useCallback(() => {
    kwaiPilotBridgeAPI.editor.rejectAllFileDiff();

    // 找出所有受影响的 qaItem 中的 file
    for (const chatId in fileStateMap) {
      for (const filepath in fileStateMap[chatId]) {
        const file = fileStateMap[chatId][filepath];
        if (file.state === "applied") {
          updateFileState(chatId, filepath, "rejected");
        }
      }
    }
    updateSqliteState("rejected");
  }, [fileStateMap, updateFileState, updateSqliteState]);

  return (
    <div className="flex items-center h-[36px] gap-2 bg-transparent px-[14px] py-[8px]">
      <div className="text-[13px] text-text-brand-default">
        {isGenerating ? "正在生成..." : STATE_LABEL_MAP[state]}
      </div>
      <div>
        变更
        {fileList.length || 0}
        {" "}
        个文件
      </div>
      {canAcceptOrReject && (
        <div className="flex items-center justify-center gap-[7px] ml-auto">
          <div
            className="flex items-center justify-center text-[13px] px-[10px] py-[12px] h-[16px] border border-solid border-border-normal hover:bg-border-bar text-text-common-primary bg-input-bg-send-disable rounded-[4px] cursor-pointer"
            onClick={handleRejectAll}
          >
            <div className="text-[13px] text-text-common-primary">全部拒绝</div>
          </div>
          <div
            className="flex items-center justify-center text-[13px] px-[10px] py-[12px] h-[16px] text-[#fff] bg-text-brand-default hover:bg-text-brand-hover rounded-[4px] cursor-pointer"
            onClick={handleAcceptAll}
          >
            <div className="text-[13px] text-text-common-primary">全部接受</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApplyStatus;
