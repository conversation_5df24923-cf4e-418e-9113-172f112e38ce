import { CustomScrollBar } from "@/components/CustomScrollbar";
import { reportUserAction } from "@/utils/weblogger";
import { ReportOpt } from "@shared/types/logger";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
  Tooltip,
  useDisclosure,
} from "@chakra-ui/react";
import { clsx } from "clsx";
import { useMemo } from "react";
import ClearKnowledgeIcon from "@/assets/icons/knowledge-clear.svg?react";
import ArrowDownIcon from "@/assets/icons/arrow-up.svg?react";
import RepoDisableIcon from "@/assets/icons/no-repo.svg?react";
import RepoSelectIcon from "@/assets/icons/repo.svg?react";
import css from "./modelArea.module.less";
import { useWorkspaceStore } from "@/store/workspace";
import { kwaiPilotBridgeAPI } from "@/bridge";
import baseInfoManager from "@/utils/baseInfo";
import eventBus from "@/utils/eventBus";
import { SharpCommand } from "@shared/types";

export const RepoSelector: React.FC = () => {
  const allRepos = useWorkspaceStore(state => state.allRepos);
  const { onOpen, onClose, isOpen, onToggle } = useDisclosure();
  const activeRepo = useWorkspaceStore(state => state.activeRepo);
  const setActiveRepo = useWorkspaceStore(state => state.setActiveRepo);

  const selectRepo = (repo: string) => {
    const params: ReportOpt<"chSetting"> = {
      key: "chSetting",
      type: "knRepo",
      content: repo,
    };
    reportUserAction(params);
    baseInfoManager.isXcode
    && eventBus.emit("pushRichEditorPanelDisableMenu", {
      [SharpCommand.FILE]: {
        status: false,
        msg: "",
      },
    });
    kwaiPilotBridgeAPI.setActiveProject(repo);
    setActiveRepo(repo);
    onClose();
  };

  const clearRepo = () => {
    setActiveRepo("");
    baseInfoManager.isXcode
    && eventBus.emit("pushRichEditorPanelDisableMenu", {
      [SharpCommand.FILE]: {
        status: true,
        msg: "未关联仓库",
      },
    });
    onClose();
  };

  const RepoTrigger = useMemo(() => {
    const name = activeRepo.split("/").pop() || "仓库";

    return (
      <div
        className={clsx(
          "hover:bg-bg-controls-hover group flex items-center gap-1 cursor-pointer rounded px-1.5 py-1 hover:",
          {
            "bg-bg-controls-hover": isOpen,
            "bg-bg-brand-active": activeRepo,
          },
        )}
        onMouseDown={e => e.preventDefault()}
        onClick={onToggle}
      >
        {!isOpen
          ? (
              <div className="w-4 h-4 flex items-center justify-center text-icon-common-secondary">
                {activeRepo
                  ? (
                      <RepoSelectIcon className="group-hover:hidden text-text-brand-default hover:"></RepoSelectIcon>
                    )
                  : (
                      <RepoDisableIcon className="group-hover:hidden "></RepoDisableIcon>
                    )}
                <ArrowDownIcon
                  className={clsx("hidden group-hover:block rotate-180", {
                    "group-hover:text-text-brand-default": activeRepo,
                  })}
                >
                </ArrowDownIcon>
              </div>
            )
          : (
              <div
                className={clsx(
                  "w-4 h-4 flex items-center justify-center transition-all",
                  {
                    "rotate-180": !isOpen,
                  },
                  [
                    activeRepo
                      ? "text-text-brand-default"
                      : " text-text-common-secondary",
                  ],
                )}
              >
                <ArrowDownIcon></ArrowDownIcon>
              </div>
            )}
        <span
          className={clsx(
            // !showText && css["model-area-item-text-hidden"],
            [
              activeRepo
                ? "text-text-brand-default"
                : "text-text-common-secondary",
            ],
            "text-[13px]",
            css["model-area-item-text-hidden"],
            "truncate",
          )}
        >
          {name}
        </span>
      </div>
    );
  }, [activeRepo, isOpen, onToggle]);

  return (
    <Popover
      isOpen={isOpen}
      onOpen={onOpen}
      onClose={onClose}
      strategy="fixed"
      placement="top-start"
    >
      <PopoverTrigger>
        <div>
          <Tooltip label={activeRepo ? `已关联${activeRepo}` : "关联仓库"}>
            {RepoTrigger}
          </Tooltip>
        </div>
      </PopoverTrigger>
      <PopoverContent
        border="none"
        w="200px"
        sx={{
          "&:focus": {
            outline: "none",
          },
          "&:focus-visible": {
            outline: "none",
            boxShadow: "none",
          },
        }}
      >
        <div className="flex flex-col bg-bg-fill border border-border-common rounded">
          <CustomScrollBar suppressScrollX className="h-[224px] w-[200px]">
            <div className="p-1 w-full flex flex-col gap-1 rounded bg-bg-fill">
              {allRepos.map((repo, idx) => {
                return (
                  <div
                    className={clsx(
                      "text-text-common-primary leading-[19.5px] text-[13px] w-full rounded-sm px-3 py-2 hover:bg-bg-hover cursor-pointer",
                      {
                        "bg-bg-selected": activeRepo === repo,
                      },
                    )}
                    onClick={() => {
                      selectRepo(repo);
                    }}
                    key={idx}
                  >
                    {repo.split("/").pop()}
                  </div>
                );
              })}
            </div>
          </CustomScrollBar>
          <div
            className="flex gap-1 h-9 items-center px-3 border-t border-border-common"
            onClick={clearRepo}
          >
            <div className="w-4 h-4 flex justify-center items-center text-text-brand-default">
              <ClearKnowledgeIcon></ClearKnowledgeIcon>
            </div>
            <span
              className="text-[13px] leading-[19.5px] text-text-brand-default cursor-pointer"
              onClick={clearRepo}
            >
              清除已选项
            </span>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};
