import { DOMExportOutput, LexicalNode, Node<PERSON>ey, SerializedTextNode, TextNode } from "lexical";

export class EmptyNode extends TextNode {
  constructor(key?: <PERSON>de<PERSON><PERSON>) {
    super("", key);
  }

  static getType(): string {
    return "empty-node";
  }

  static clone(node: EmptyNode): EmptyNode {
    return new EmptyNode(node.__key);
  }

  static override importJSON(_serializedNode: SerializedTextNode): EmptyNode {
    return new EmptyNode();
  }

  createDOM() {
    const dom = document.createElement("span");
    return dom;
  }

  exportDOM(): DOMExportOutput {
    const element = document.createElement("span");
    element.setAttribute("data-lexical-mention", "true");
    element.textContent = this.__text;
    return { element };
  }

  exportJSON() {
    return {
      ...super.exportJSON(),
      type: EmptyNode.getType(),
      version: 1,
    };
  }
}

export function $createEmptyNode() {
  const emptyNode = new EmptyNode();
  emptyNode.setMode("token");
  return emptyNode;
}

export function $isEmptyNode(
  node: LexicalNode | null | undefined,
): node is EmptyNode {
  return node instanceof EmptyNode;
}
