import React, { useEffect, useRef, useState } from "react";

import { Tooltip, TooltipProps } from "@chakra-ui/react";
import clsx from "clsx";

import "./index.less";
import useResizeObserver from "@/hooks/useResizeObserver";

export interface IAutoTooltipProps extends Omit<TooltipProps, "children"> {
  /** 使用 line-clamp ellipsis */
  lineClamp?: number;
  tooltipClassName?: string;
  children?: React.ReactNode;
}

type Props = IAutoTooltipProps;

const AutoTooltip: React.FC<Props> = ({
  title,
  className,
  tooltipClassName,
  lineClamp,
  children,
  ...rest
}) => {
  const [visible, setVisible] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const wrapRef = useRef<HTMLSpanElement>(null);
  const rect = useResizeObserver(wrapRef);

  useEffect(() => {
    if (wrapRef.current) {
      if (lineClamp) {
        if (wrapRef.current.scrollHeight > wrapRef.current.clientHeight) {
          return setVisible(true);
        }
      }
      else {
        if (wrapRef.current.scrollWidth > wrapRef.current.offsetWidth) {
          return setVisible(true);
        }
      }
    }
    return setVisible(false);
  }, [rect, lineClamp, children]);

  // 添加滚动事件监听器，在滚动时关闭 tooltip
  useEffect(() => {
    const handleScroll = () => {
      if (isOpen) {
        setIsOpen(false);
      }
    };

    window.addEventListener("scroll", handleScroll, true);
    return () => {
      window.removeEventListener("scroll", handleScroll, true);
    };
  }, [isOpen]);

  return (
    <Tooltip
      label={visible ? title : ""}
      className={tooltipClassName}
      isOpen={visible && isOpen}
      onOpen={() => setIsOpen(true)}
      onClose={() => setIsOpen(false)}
      closeOnScroll={true}
      {...rest}
    >
      <span
        className={clsx("k-common-auto-tooltip", className, {
          "line-clamp": lineClamp,
        })}
        ref={wrapRef}
        style={
          lineClamp ? { lineClamp, WebkitLineClamp: lineClamp } : undefined
        }
      >
        {children}
      </span>
    </Tooltip>
  );
};

export default AutoTooltip;
