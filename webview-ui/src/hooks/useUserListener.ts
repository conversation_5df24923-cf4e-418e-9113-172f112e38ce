import { useCallback, useEffect, useRef } from "react";
import { useUserStore } from "@/store/user";
import baseInfoManager from "@/utils/baseInfo";
import { reportFMP, updateCommonPackage } from "@/utils/weblogger";

import { kwaiPilotBridgeAPI } from "@/bridge";
import repoChatService from "@/services/repo-chat";
import eventBus from "@/utils/eventBus";
import { getRecordStoreByVendor } from "@/store/record";
import { getDialogSetting } from "@/utils/sessionUtils";
import { getLocalStorageValue, setLocalStorageValue } from "@/utils/localStorage";
import { logger } from "@/utils/logger";

let firstTime = true;
// 标记是否是页面加载后的第一次初始化
let isInitialLoad = true;
// 跟踪最后一次会话恢复的时间，防止频繁调用
let lastSessionRestoreTime = 0;

const recordStoreChatVendor = getRecordStoreByVendor("chat");
const recordStoreComposerVendor = getRecordStoreByVendor("composer");

export const useUserListener = () => {
  const setUserInfo = useUserStore(state => state.setUserInfo);
  // 用于跟踪会话恢复的状态，防止多次触发
  const restoringSessionRef = useRef(false);

  const resetRecord = useCallback(() => {
    recordStoreChatVendor.getState().finishLoading();
    recordStoreChatVendor
      .getState()
      .setActiveSession({ value: "", updateLocalStorage: false });
    recordStoreComposerVendor.getState().finishLoading();
    recordStoreComposerVendor
      .getState()
      .setActiveSession({ value: "", updateLocalStorage: false });
  }, []);

  const initSessionList = useCallback(async () => {
    // 避免频繁调用 (2秒内不重复初始化)
    const now = Date.now();
    if (now - lastSessionRestoreTime < 2000) {
      return;
    }
    lastSessionRestoreTime = now;

    // 避免并发调用
    if (restoringSessionRef.current) {
      return;
    }
    restoringSessionRef.current = true;

    try {
      const dialogSetting = getDialogSetting();
      const { value: activeSessionId }
        = await kwaiPilotBridgeAPI.getState<string>("activeSessionId");
      const { value: activeComposerSessionId }
        = await kwaiPilotBridgeAPI.getState<string>("activeComposerSessionId");

      // 为聊天记录初始化设置
      recordStoreChatVendor.getState().initSetting(dialogSetting);
      recordStoreComposerVendor.getState().initSetting(dialogSetting);

      // 如果路径是首页，记录当前活跃会话ID，以便在页面加载时恢复
      const currentPath = getLocalStorageValue("activePath") || "/";
      const isHomePage = currentPath === "/" || !currentPath;

      // 设置聊天会话
      if (activeSessionId) {
        // 先检查会话数据
        const data = await kwaiPilotBridgeAPI.getSession({ sessionId: activeSessionId });

        if (data) {
          // 有会话数据时再设置活跃会话
          recordStoreChatVendor.getState().setActiveSession({
            value: activeSessionId,
            navigateToChat: false, // 先不触发导航
            updateHistory: true,
          });

          logger.info("Session data loaded", "useUserListener.ts", {
            value: activeSessionId,
          });

          // 在首次加载时，如果当前在首页且有活跃会话，则导航到聊天页面
          if (isInitialLoad && isHomePage) {
            // 设置路径为chat，确保下次重载时能直接进入chat页面
            setLocalStorageValue("activePath", "/chat");

            // 确保会话数据加载完毕后再导航
            setTimeout(() => {
              logger.info("Navigating to chat page", "useUserListener.ts");
              eventBus.emit("chat");
            }, 300); // 增加延迟确保数据已加载
          }
        }
        else {
          // 如果找不到会话数据，则清空activeSession
          recordStoreChatVendor.getState().setActiveSession({
            value: "",
            updateLocalStorage: true,
          });
          logger.warn("Session data not found", "useUserListener.ts", {
            value: activeSessionId,
          });
        }
      }

      // 设置助理会话
      if (activeComposerSessionId) {
        recordStoreComposerVendor.getState().setActiveSession({
          value: activeComposerSessionId,
        });
      }

      // 标记已完成初始加载
      isInitialLoad = false;
    }
    catch (err) {
      logger.error("Failed to initialize session", "useUserListener.ts", { err });
    }
    finally {
      restoringSessionRef.current = false;
    }
  }, []);

  useEffect(() => {
    // 在页面加载时，立即初始化会话列表
    initSessionList();
  }, [initSessionList]);

  useEffect(() => {
    kwaiPilotBridgeAPI.getAndWatchUserInfo((userInfo) => {
      setUserInfo(userInfo);
      if (userInfo) {
        // 用户登录后初始化会话
        initSessionList();
        window.userInfo = userInfo;
        repoChatService.initCodeSearch();
        if (firstTime) {
          firstTime = false;
          reportFMP();
          updateCommonPackage({
            user_id: userInfo.name,
          });
        }
      }
      else {
        eventBus.emit("home");
        resetRecord();
      }
    });

    // 添加可见性变化的处理，确保在webview聚焦时重新加载会话
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible") {
        // 页面变为可见时重新检查会话状态
        initSessionList();
      }
    };
    document.addEventListener("visibilitychange", handleVisibilityChange);

    // 监听会话状态变化
    const handleStorage = (e: StorageEvent) => {
      // 当localStorage变化时，检查是否需要更新会话
      if (e.key === "activeSessionId" && e.newValue) {
        initSessionList();
      }
    };
    window.addEventListener("storage", handleStorage);

    kwaiPilotBridgeAPI.getSystemInfo().then((data) => {
      baseInfoManager.updateIdeVersion(data.ideVersion);
      baseInfoManager.updatePluginVersion(data.pluginVersion);
      baseInfoManager.updatePlatform(data.platform);
      baseInfoManager.updateRelease(data.release);
      baseInfoManager.updateDeviceId(data.deviceId);
      baseInfoManager.updateIde(data.ide);
      baseInfoManager.updateHostname(data.hostname);
      baseInfoManager.updateMachine(data?.machine);
      baseInfoManager.updateArch(data?.arch);
      baseInfoManager.updateVersion(data?.version);
    });
    kwaiPilotBridgeAPI.extensionComposer.$getWorkspaceFile().then((workspaceFile) => {
      const isInWorkspace = Boolean(workspaceFile);
      baseInfoManager.updateIsInWorkspace(isInWorkspace);
    });

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange);
      window.removeEventListener("storage", handleStorage);
    };
  }, [initSessionList, resetRecord, setUserInfo]);

  return null;
};
