import { useColorMode } from "@chakra-ui/react";
import { useCallback } from "react";
import Logo from "@/pages/home/<USER>";
import Footer from "@/pages/home/<USER>";
import { UserInputTextArea } from "@/logics/composer/components/UserInputTextArea/UserInputTextArea";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { RestoreConfirmDialog, RestoreConfirmDialogContext } from "@/logics/composer/components/HumanMessage/RestoreConfirmDialog";
import { RestoreAndSendDialog, RestoreAndSendDialogContext } from "@/logics/composer/components/HumanMessage/RestoreAndSendDialog";
import { SendConfirmDialog, SendConfirmDialogContext } from "@/logics/composer/components/HumanMessage/SendConfirmDialog";
import { withProviders } from "@udecode/cn";

export const EmptySession = withProviders(RestoreConfirmDialogContext, RestoreAndSendDialogContext, SendConfirmDialogContext)(() => {
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";
  const bgStyle = theme === "light" ? "" : "bg-[#091524]";

  const openUserManual = useCallback(() => {
    kwaiPilotBridgeAPI.openUrl("https://docs.corp.kuaishou.com/k/home/<USER>");
  }, []);
  return (
    <div
      className={`${bgStyle} flex h-screen w-screen min-w-[275px] flex-col justify-center items-center`}
    >

      <div className="w-full flex h-full px-[16px] flex-col justify-start items-center overflow-auto">
        <div className="h-[350px] w-full  flex items-end justify-center relative">
          <Logo></Logo>
        </div>
        <div className="h-[242px] min-h-[242px] max-h-[242px]  w-full mt-[40px] flex flex-col items-center relative">
          {isDark && (
            <div className="absolute bottom-[calc(100%-12px)] h-[300px] w-[100vw] min-w-[272px] overflow-hidden flex justify-center">
              <div className="w-[1136px] h-[300px] bg-cover bg-center bg-[url('https://ali.a.yximgs.com/kos/nlav12119/xEamZryG_2024-08-29-16-47-50.png')]"></div>
            </div>
          )}

          <RestoreConfirmDialog />
          <RestoreAndSendDialog />
          <SendConfirmDialog />
          <div className="w-[calc(100%-4px)]">
            <UserInputTextArea
              editorClassName="h-[80px]"
              enable={{
                top: true,
                right: false,
                bottom: false,
                left: false,
                topRight: false,
                bottomRight: false,
                bottomLeft: false,
                topLeft: false,
              }}
              hiddenApplyStatus
              role="bottom"
            />
          </div>
          <div className="mt-4 flex gap-[6px] text-[12px] leading-[18px] text-[#B4BCD0]">
            更多能力详见
            <div className="text-[#5AA7FF] cursor-pointer" onClick={openUserManual}>Kwaipilot使用手册</div>
          </div>
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
});
