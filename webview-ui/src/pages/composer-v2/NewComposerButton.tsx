import NewDialogIcon from "@/assets/icons/new-dialog.svg?react";
import { Tooltip } from "@chakra-ui/react";

export const NewComposerButton: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  return (
    <Tooltip label="新会话">
      <div
        onClick={onClick}
        className="overflow-hidden flex gap-1 items-center p-[2px] cursor-pointer rounded text-icon-common-secondary hover:text-text-brand-default"
      >
        <NewDialogIcon className="w-[13.5px] h-[13.5px]" />
        <div
          className="text-[13px] leading-[20px] whitespace-nowrap truncate flex items-center"
        >
          新会话
        </div>
      </div>
    </Tooltip>
  );
};
