import { TextArea } from "@/components/TextArea";
import useNavigateWithCache from "@/hooks/useNavigateWithCach";
import eventBus from "@/utils/eventBus";
import { useColorMode } from "@chakra-ui/react";
import { useCallback, useEffect, useRef } from "react";
import Logo from "./Logo";
import Footer from "./Footer";
import Cards from "./Cards";
import { HistoryBar } from "@/components/HistoryBar";
import { RecordProvider } from "@/store/record";
import { NotLoggedHint } from "@/components/NotLoggedHint";
import { useUserStore } from "@/store/user";

export const Home = () => {
  const { colorMode: theme } = useColorMode();
  const navigate = useNavigateWithCache();

  const userInfo = useUserStore(state => state.userInfo);
  const goChat = useCallback(() => {
    navigate("/chat");
  }, [navigate]);
  useEffect(() => {
    eventBus.on("chat", goChat);
    return () => {
      eventBus.off("chat", goChat);
    };
  }, [goChat]);
  const isDark = theme === "dark";
  const textHeaderStyle
    = theme === "light"
      ? {
          border: "1px solid white",
          background:
            "linear-gradient(100deg, rgba(224, 240, 255, 0.60) 1.25%, rgba(229, 243, 255, 0.60) 14.55%, rgba(214, 234, 255, 0.60) 95.73%)",
        }
      : {
          border: "1px solid #08203d",
          background:
            "linear-gradient(100deg, #002950 1.25%, #0D2B4D 31.83%, #172B40 95.73%)",
        };
  const bgStyle = theme === "light" ? "" : "bg-[#091524]";
  const signIcon
    = theme === "light"
      ? "bg-[url('https://ali.a.yximgs.com/kos/nlav12119/vaIcoKxj_2024-08-29-13-42-28.png')]"
      : "bg-[url('https://ali.a.yximgs.com/kos/nlav12119/ewrNsyje_2024-08-29-13-41-20.png')]";
  const repoStyle = theme === "light" ? "text-[#2A3B51]" : "text-[#E5ECF2]";
  const signRef = useRef<any>(null);
  const setSignRef = useCallback((ref: any) => {
    signRef.current = ref;
  }, []);
  const handleQuick = useCallback(() => {
    signRef.current?.current?.insertSign?.("#");
  }, [signRef]);
  return (
    <div
      className={`${bgStyle} flex h-screen w-screen min-w-[275px] flex-col justify-center items-center`}
    >
      {!isDark && (
        <>
          <video
            className="absolute z-[-2] object-cover min-w-[272px] w-full h-full"
            autoPlay
            muted
            loop
          >
            <source src="https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/external-assets/kwaipilot-ide-light-bg.mp4" />
          </video>
          <div className="absolute z-[-1] min-w-[272px] w-full h-full opacity-65 bg-[linear-gradient(180deg,rgba(255,255,255,0.64)_0%,rgba(255,255,255,0.80)_16%,rgba(255,255,255,0.85)_100%)]"></div>
        </>
      )}
      <HistoryBar />

      <div className="w-full flex h-full px-[16px] flex-col justify-start items-center overflow-auto">
        <div className="min-h-[292px] w-full flex-1 flex items-end justify-center relative">
          <Logo></Logo>
        </div>
        {userInfo
          ? (
              <div className="h-[242px] min-h-[242px] pt-[12px] w-full mt-[40px] flex flex-col items-center relative">
                {isDark && (
                  <div className="absolute bottom-[calc(100%-12px)] h-[300px] w-[100vw] min-w-[272px] overflow-hidden flex justify-center">
                    <div className="w-[1136px] h-[300px] bg-cover bg-center bg-[url('https://ali.a.yximgs.com/kos/nlav12119/xEamZryG_2024-08-29-16-47-50.png')]"></div>
                  </div>
                )}
                <div
                  className="w-full  overflow-hidden flex justify-between items-center mb-[-6px] p-[12px] pb-[18px] rounded-t-[8px]"
                  style={textHeaderStyle}
                >
                  <div className="w-[calc(100%-60px)] flex gap-[4px] items-center">
                    <div
                      className={`min-w-[22px] size-[22px] ${signIcon} bg-center bg-cover bg-no-repeat`}
                    >
                    </div>
                    <div
                      className={`text-[13px] font-bold min-w-[52px] ${repoStyle}`}
                    >
                      代码仓库
                    </div>
                    <div
                      className="text-[12px] text-[#6E849D] truncate"
                      title="通过自然语言高效发掘代码仓库隐性知识"
                    >
                      通过自然语言高效发掘代码仓库隐性知识
                    </div>
                  </div>
                  <div
                    className="text-[13px] text-text-brand-default hover:text-text-brand-hover min-w-[52px] cursor-pointer"
                    onClick={handleQuick}
                  >
                    立即体验
                  </div>
                </div>
                <div className="w-[calc(100%-4px)]">
                  <RecordProvider vendor="chat">
                    <TextArea
                      editorClassName="h-[80px]"
                      forwardedRefInsertCommandSignRef={setSignRef}
                    />
                  </RecordProvider>
                </div>
              </div>
            )
          : (
              <div className=" mt-9 bg-input-bg-input-fill rounded-lg border-2 border-solid border-[#33455A]">
                <NotLoggedHint pt={2} px={4} />
              </div>
            )}
        <div className="min-h-[260px] h-full w-full">
          <Cards></Cards>
        </div>
      </div>
      <Footer></Footer>
    </div>
  );
};
