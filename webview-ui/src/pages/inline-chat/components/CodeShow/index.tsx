import { useEffect, useRef, memo, useState } from "react";

import { useColorMode, useBoolean } from "@chakra-ui/react";
import { getHighlighterInstance } from "@/utils/highlighter";
import { getShikiLanguage } from "@/utils/language2shikiLang";
import { getShikiTheme } from "@/utils/theme2shikiTheme";
import { useVsEditorConfig } from "@/store/vsEditorConfig";

export interface IHighlightProps {
  language?: string;
  content: string;
  lineNumber: number;
  borderRadius?: string;
}
const showCodeLineNumber = 4;

export function CodeShow(props: IHighlightProps) {
  const { language = "typescript", content, lineNumber } = props;
  const { colorMode: theme } = useColorMode();
  const containerRef = useRef<HTMLDivElement>(null);
  const [showCollapse, setShowCollapse] = useBoolean();
  const [isCollapse, setIsCollapse] = useState<boolean>(true);
  const canCollapse = lineNumber > showCodeLineNumber;
  const editorConfig = useVsEditorConfig(state => state.editorConfig);
  const [code, setCode] = useState("");

  useEffect(() => {
    if (containerRef?.current) {
      // 可能有横向滚动条，使用 50 判断
      containerRef?.current?.scrollHeight > 50
        ? setShowCollapse.on()
        : setShowCollapse.off();
    }
    const highlighter = getHighlighterInstance();
    const html = highlighter.codeToHtml(content, {
      lang: getShikiLanguage(language) ?? "typescript",
      theme: getShikiTheme(editorConfig.theme),
    });
    const transparentHtml = html
      .replace(
        "<code>",
        `<code style="font-family:${editorConfig.fontFamily}">`,
      )
      .replace(
        /background-color:(#[0-9a-fA-F]{3,8}|[a-zA-Z]+)/,
        `background-color:transparent`,
      );
    setCode(transparentHtml);
  }, [
    setShowCollapse,
    content,
    language,
    editorConfig.theme,
    editorConfig.fontFamily,
  ]);

  return (
    <>
      <div
        className={` overflow-y-hidden  custom-scrollbar-${theme} dialog-item-syntax-wrapper ${
          isCollapse && "max-h-[88px]"
        } ${showCollapse && "overflow-y-hidden no-scrollbar"}`}
        ref={containerRef}
      >
        <div dangerouslySetInnerHTML={{ __html: code }}></div>
      </div>
      {canCollapse && (
        <div
          className="flex justify-center pb-1  rounded-b-[8px] bg-transparent"
        >
          <span
            className={`cursor-pointer flex gap-1 items-center ${
              theme === "dark" ? "text-[#588BFC]" : "text-[#326BFB]"
            }`}
            onClick={() => setIsCollapse(s => !s)}
          >
            {isCollapse
              ? `展开 ${lineNumber - showCodeLineNumber} 行代码`
              : "收起代码"}
            <div className={isCollapse ? "" : "rotate-180"}>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="10.065871238708496"
                height="5.690796852111816"
                viewBox="0 0 10.065871238708496 5.690796852111816"
              >
                <path
                  d="M9.87223 1.12278 L5.49723 5.49778 C5.43626 5.55896 5.36382 5.6075 5.28405 5.64062 C5.20428 5.67375 5.11876 5.6908 5.03239 5.6908 C4.94602 5.6908 4.86049 5.67375 4.78073 5.64062 C4.70096 5.6075 4.62851 5.55896 4.56754 5.49778 L0.192545 1.12278 C0.0692604 0.999495 -1.83708e-09 0.832285 0 0.657935 C1.83708e-09 0.483585 0.0692604 0.316376 0.192545 0.193091 C0.315829 0.0698072 0.483038 0.000546994 0.657388 0.000546992 C0.831739 0.00054699 0.998948 0.0698072 1.12223 0.193091 L5.03294 4.10379 L8.94364 0.192545 C9.06692 0.0692606 9.23413 0 9.40848 0 C9.58283 0 9.75004 0.0692606 9.87333 0.192545 C9.99661 0.315829 10.0659 0.483038 10.0659 0.657389 C10.0659 0.831739 9.99661 0.998948 9.87333 1.12223 L9.87223 1.12278 Z"
                  fill="#588BFC"
                  fillRule="nonzero"
                />
              </svg>
            </div>
          </span>
        </div>
      )}
    </>
  );
}

export default memo(CodeShow);
