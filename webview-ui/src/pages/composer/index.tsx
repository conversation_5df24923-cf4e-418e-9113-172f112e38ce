import { Dialog } from "@/components/Dialog";
import { HistoryBar } from "@/components/HistoryBar";
import VideoBg from "@/components/VideoBg";
import { Alert, AlertIcon, useColorMode } from "@chakra-ui/react";
import {
  RecordProvider,
  useRecordStore,
} from "@/store/record";
import { NewComposerButton } from "./NewComposerButton";
import { withProps, withProviders } from "@udecode/cn";

const InnerPageComposer = () => {
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";

  const sessionHistory = useRecordStore(state => state.sessionHistory);

  return (
    <div className="flex flex-col h-full min-w-[299px] overflow-scroll overflow-y-hidden bg-main-bg">
      {!isDark && <VideoBg />}
      <HistoryBar current="composer" />
      {sessionHistory && !sessionHistory.isComposer
        ? (
            <div className="text-text-common-secondary px-6 py-6 flex items-center gap-2">
              <div className="mt-[1px]">欢迎体验助理模式(beta版), 请创建</div>
              <NewComposerButton />
            </div>
          )
        : (
            <>
              <div className="flex flex-col justify-between w-full h-full overflow-hidden">
                <div className="overflow-y-scroll overscroll-none px-[12px] pt-3 flex flex-col-reverse chat-dialog-container">
                  <div className="w-full">
                    <Dialog />
                  </div>
                </div>
              </div>

              <Alert status="info">
                <AlertIcon />
                当前浏览的消息为旧版助理模式的记录，仅保留查看功能，欢迎体验新版智能体模式
              </Alert>
            </>
          )}
    </div>
  );
};

export const PageComposer = withProviders(
  withProps(RecordProvider, { vendor: "composer" }),
)(InnerPageComposer);
