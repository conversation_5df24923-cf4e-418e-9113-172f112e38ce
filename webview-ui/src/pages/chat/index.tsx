import { Dialog } from "@/components/Dialog";
import { TextArea } from "@/components/TextArea";
import useNavigateWithCache from "@/hooks/useNavigateWithCach";
import {
  RecordProvider,
  useRecordStore,
} from "@/store/record";
import eventBus from "@/utils/eventBus";
import { collectClick } from "@/utils/weblogger";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import StopIcon from "@/assets/stop.svg?react";
import clsx from "clsx";
import { useColorMode } from "@chakra-ui/react";
import VideoBg from "@/components/VideoBg";
import { HistoryBar } from "@/components/HistoryBar";
import DialogMask from "./components/DialogMask";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { NewDialog } from "@/components/TextArea/components/NewDialog";
import { withProps, withProviders } from "@udecode/cn";
import { useUserStore } from "@/store/user";
import { NotLoggedHint } from "@/components/NotLoggedHint";

const InnerChat = () => {
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";
  const finishLoading = useRecordStore(state => state.finishLoading);
  const loadingStatu = useRecordStore(state => state.loadingStatu);
  const sessionHistory = useRecordStore(state => state.sessionHistory);
  const abortCurrentChat = useRecordStore(state => state.abortCurrentChat);
  const signalMessageDone = useRecordStore(state => state.signalMessageDone);
  const setStopReceiveMessageId = useRecordStore(
    state => state.setStopReceiveMessageId,
  );

  const userInfo = useUserStore(state => state.userInfo);

  const navigate = useNavigateWithCache();
  const [showMask, setShowMask] = useState(false);

  const goHome = useCallback(() => {
    navigate("/");
  }, [navigate]);
  useEffect(() => {
    eventBus.on("home", goHome);
    return () => {
      eventBus.off("home", goHome);
    };
  }, [goHome]);
  const onStop = () => {
    abortCurrentChat();
    // 置为 loading false
    if (sessionHistory) {
      const currentQA = sessionHistory.cachedMessages.find(
        v => v.id === loadingStatu?.id,
      );
      currentQA
      && kwaiPilotBridgeAPI.addMessage({
        item: currentQA,
        sessionId: sessionHistory.sessionId,
        chatId: loadingStatu?.id ?? "",
      });
    }
    setStopReceiveMessageId(loadingStatu?.id ?? "");
    signalMessageDone(loadingStatu?.id ?? "");
    finishLoading();
    collectClick("VS_STOP_BUTTON");
  };
  const renderStopButton = () => (
    <div
      onClick={onStop}
      className="absolute left-[calc(50%-42px)] -top-[44px] h-[34px] p-[8px] rounded-[4px] bg-bg-fill flex items-center gap-[4px] hover:bg-bg-controls-hover cursor-pointer shadow-stop-button"
    >
      <div className={clsx(isDark ? "#E5ECF2" : "#77808B")}>
        <StopIcon />
      </div>
      <div className="text-[13px] font-normal leading-[18px] text-text-common-primary">
        停止生成
      </div>
    </div>
  );

  const dialogMask = useMemo(() => {
    if (!isDark) {
      return {
        background:
          "linear-gradient(0deg, #F5FBFF 0%, rgba(246, 251, 255, 0.00) 100%)",
        backdropFilter: "blur(0.25px)",
      };
    }
    return {
      background:
        "linear-gradient(0deg, #091420 0%, rgba(9, 20, 32, 0.00) 100%)",
      backdropFilter: " blur(0.25px)",
    };
  }, [isDark]);
  const dialogContainer = useRef(null);
  const inputContainer = useRef(null);

  return (
    <div className="flex flex-col h-full min-w-[299px] overflow-scroll overflow-y-hidden bg-main-bg">
      {!isDark && <VideoBg />}
      <HistoryBar action={<NewDialog />} />
      { !userInfo
        ? <NotLoggedHint p={4} />
        : sessionHistory && sessionHistory.isComposer
          ? (
              <div className="text-text-common-secondary px-6 py-6 flex items-center gap-2">
                <div className="mt-[1px]">
                  当前处于助理模式(beta版), 如需AI对话 请创建
                </div>
                <NewDialog />
              </div>
            )
          : (
              <>
                <div
                  ref={dialogContainer}
                  className="flex flex-col justify-between w-full h-full overflow-hidden"
                >
                  <div className="overflow-y-scroll overscroll-none px-[12px] pt-3 flex flex-col-reverse chat-dialog-container">
                    <div className="w-full">
                      <Dialog />
                      <DialogMask setShowMask={setShowMask} />
                    </div>
                  </div>
                </div>
                <div ref={inputContainer} className="flex-none relative">
                  <div
                    className={clsx(
                      "h-6  w-full absolute -top-[18px] pointer-events-none transition-opacity",
                      showMask ? "opacity-100" : "opacity-0",
                    )}
                    style={{
                      ...dialogMask,
                    }}
                  >
                  </div>
                  {loadingStatu
                  && loadingStatu.status === "loading"
                  && renderStopButton()}
                  <TextArea
                    editorClassName="min-h-[44px]"
                    wrapperClassName="p-3 max-h-[400px]"
                    enable={{
                      top: true,
                      right: false,
                      bottom: false,
                      left: false,
                      topRight: false,
                      bottomRight: false,
                      bottomLeft: false,
                      topLeft: false,
                    }}
                  />
                </div>
              </>
            )}
    </div>
  );
};

export const Chat = withProviders(
  withProps(RecordProvider, {
    vendor: "chat",
  }),
)(InnerChat);
