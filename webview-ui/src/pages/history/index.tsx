import { CustomScrollBar } from "@/components/CustomScrollbar";
import { HistoryBar } from "@/components/HistoryBar";
import VideoBg from "@/components/VideoBg";
import useNavigateWithCache from "@/hooks/useNavigateWithCach";
import eventBus from "@/utils/eventBus";
import { useColorMode } from "@chakra-ui/react";
import { useEffect } from "react";
import HistoryContent from "./Content";
import ClearHistory from "./ClearHistory";

const History = () => {
  const { colorMode: theme } = useColorMode();
  const isDark = theme === "dark";
  const navigate = useNavigateWithCache();
  useEffect(() => {
    eventBus.on("home", () => {
      navigate("/");
    });
    eventBus.on("chat", () => {
      navigate("/chat");
    });
  }, [navigate]);
  return (
    <div className="flex flex-col  h-full min-w-[299px] overflow-scroll overflow-y-hidden bg-main-bg">
      <HistoryBar current="history" action={<ClearHistory />} />
      {!isDark && <VideoBg />}
      <div className="flex flex-col justify-between w-full h-full overflow-hidden">
        <CustomScrollBar className="overflow-y-scroll overscroll-none px-[12px] pt-3">
          <HistoryContent />
        </CustomScrollBar>
      </div>
    </div>
  );
};

export default History;
