/*
禁用 tailwin preflight 手动引入一部分必要的 reset
*/

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: theme('borderColor.DEFAULT', currentColor); /* 2 */
}

::before,
::after {
  --tw-content: '';
}


@tailwind base;
@tailwind components;
@tailwind utilities;


body {
  padding: 0;
}
body.vscode-dark{
  background: #050e18!important;
}
body.vscode-light{
  background: linear-gradient(170deg, #FCFDFD 0%, #EDF3FD 72.59%)!important;
}

.main-text-dark{
  color: white;
}
.main-text-light{
  color: #262A2F;
}
.sub-text-dark{
  color: #B4BCD0;
}
.sub-text-light{
  color: #676D75;
}
.main-card-bg-dark{
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.10) 0%, rgba(255, 255, 255, 0.07) 100%);
}
.main-card-bg-light{
  border: 1px solid #FFF;
  background: rgba(255, 255, 255, 0.70);
}

.tag-bg-dark{
  background: rgba(255, 255, 255, 0.08);
}
.tag-bg-light{
  background: #F5F6F8;
}

.icon-fill-dark{
  fill: white;
}
.icon-fill-light{
  fill: #262A2F;
}
.icon-avatar-fill-dark{
  fill: #8B949E;
}
.icon-avatar-fill-light{
  fill: #8B949E;
}
.icon-sub-fill-dark{
  fill: white;
}
.icon-sub-fill-light{
  fill: #676D75;
}
.icon-edit-fill-dark{
  fill: #8B949E;
}
.icon-edit-fill-light{
  fill: #676D75;
}

.blue-text-dark {
  color: #5AA7FF;
}

.blue-text-light {
  color: #326BFB;
}

.icon-fill-blue-dark {
  fill: #5AA7FF;
}

.icon-fill-blue-light {
  fill: #326BFB;
}

.link-text-dark{
  color: #ffffff;
}

.link-text-dark a {
  color: #5AA7FF;
  cursor: pointer;
}

.link-text-light{
  color: #212429;
}

.link-text-light a {
  color: #326BFB;
  cursor: pointer;
}

.upload-card-bg-dark{
  background: #2F373F;
  color: #fff;
}
.upload-card-bg-light{
  background: #FBFCFD;
  color: #262A2F;
}

/* 滚动条样式 */
.no-scrollbar::-webkit-scrollbar {
  width: 0;
  background: transparent;
}
.custom-scrollbar-dark::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}     
.custom-scrollbar-dark::-webkit-scrollbar-track {
  background: rgba(#0F161B, 0.1);
  border-radius: 6px;
}    
.custom-scrollbar-dark::-webkit-scrollbar-thumb {
  background: #0F161B;
  border-radius: 6px;
}    
.custom-scrollbar-dark::-webkit-scrollbar-thumb:hover { 
  background: #0F161B;
}

.custom-scrollbar-light::-webkit-scrollbar {
  width: 6px;
}
.custom-scrollbar-light::-webkit-scrollbar-track {
  background: rgba(#D0D7DE, 0.1);
  border-radius: 6cm;
}    
.custom-scrollbar-light::-webkit-scrollbar-thumb {
  background: #D0D7DE;
  border-radius: 6px;
}    
.custom-scrollbar-light::-webkit-scrollbar-thumb:hover { 
  background: #898A8C;
}

.kwaipilot-rich-editor-menu:hover .kwaipilot-rich-editor-menu-description{
  display: flex;
}