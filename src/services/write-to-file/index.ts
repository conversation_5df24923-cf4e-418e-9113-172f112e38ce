import { ServiceModule } from "..";
import { Bridge } from "../../base/bridge";
import { ContextManager } from "../../base/context-manager";
import * as vscode from "vscode";
import * as os from "os";
import * as path from "path";
import { NATIVE_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { DIFF_VIEW_URI_SCHEME, DiffViewProvider } from "../../core/diff/diffViewProvider";
import { fileExistsAtPath } from "../../utils/fs";
import { DiffModule } from "../../core/diff";
import { LoggerManager } from "../../base/logger";
import * as fs from "fs/promises";
import { v4 as uuidv4 } from "uuid";
import { GlobalStateManager } from "../../base/state-manager";
import { GlobalState } from "shared/lib/state-manager/types";
import { PLUGIN_PLATFORM } from "../../log/Model";
import { Api } from "../../base/http-client";
import { EditFileRequest, EditFileResponse } from "shared/lib/agent";
import { createDeferred, Deferred } from "../../utils/deferred";
import { arePathsEqual } from "../../utils/path";
import { fixModelHtmlEscaping, removeInvalidChars } from "../../utils/string";
import { WebloggerManager } from "../../base/weblogger";
import { ReportOpt } from "shared/lib/misc/logger";
import { ComposerService } from "../composer";

// 错误类型定义
type FileProcessError = {
  filePath: string;
  errorType: "timeout" | "aborted" | "processing" | "content" | "network" | "unknown";
  message: string;
  timestamp: number;
};

type WriteToFilePayload = {
  path: string;
  content?: string;
  diff?: string;
  isFinal?: boolean;
};

/* editFile 所在消息的上下文信息 */
export interface EditFileContext {
  sessionId: string;
  chatId: string;
  /* 触发 editFile 的来源: 新版助理模式 or 旧版 markdown 编辑器（对话模式、instant-apply） */
  source: "agent-v2" | "markdown-body" | "inline-chat";
}

interface EditFileState {
  context: EditFileContext;
  messageQueue: MessageQueue | undefined;
  deferred: Deferred<EditFileResponse>;
  requestController: AbortController;
  applyId: string;
}
interface DiffState {
  diffViewProvider: DiffViewProvider;
  context: EditFileContext;
  applyId: string;
}
export class WriteToFileService extends ServiceModule {
  private loggerScope = "WriteToFileService";

  /**
   * 在完成 editFile 之后 diff 时保存的状态
   */
  diffState: Map<string, DiffState> = new Map();
  /**
   * 在执行 editFile 期间（从调用.editFile 到用户确认（accept/reject））存储的状态，
   *
   * key: 文件路径
   * value: 文件状态 {EditFileState}
   */
  state = new Map<string, EditFileState>();

  private fileProcessingQueue: string[] = [];
  private isProcessingFile = false;
  private processingLock = false;
  private recentErrors: Map<string, FileProcessError> = new Map(); // 记录最近的错误
  private cwd: string;
  constructor(private readonly ctx: ContextManager) {
    super(ctx);
    this.cwd = vscode.workspace.workspaceFolders?.map(folder => folder.uri.fsPath).at(0) ?? path.join(os.homedir(), "Desktop");
    this.registerKwaipilotSchema();

    // 注册监听器
    this.registerListener();

    // 注册导航命令
    this.registerNavigationCommands();

    // 监听编辑器变化事件，更新diff导航状态
    vscode.window.onDidChangeActiveTextEditor(() => {
      this.updateDiffNavigationContext();
    }, null, this.context.subscriptions);
  }

  private registerKwaipilotSchema() {
    const diffContentProvider = new (class implements vscode.TextDocumentContentProvider {
      provideTextDocumentContent(uri: vscode.Uri): string {
        return Buffer.from(uri.query, "base64").toString("utf-8");
      }
    })();
    this.context.subscriptions.push(vscode.workspace.registerTextDocumentContentProvider(DIFF_VIEW_URI_SCHEME, diffContentProvider));
  }

  getFileDiffState(relPath: string) {
    return this.diffState.get(relPath);
  }

  private registerListener() {
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.OPEN_FILE_TO_EDITOR_MAYBE_DIFF_EDITOR, async (payload?: {
      filepath: string;
    }) => {
      if (!payload) {
        this.logger.warn("收到无效的 OPEN_FILE_TO_EDITOR_MAYBE_DIFF_EDITOR 事件", this.loggerScope);
        return;
      }
      const diffViewProvider = this.diffState.get(payload.filepath)?.diffViewProvider;
      if (!diffViewProvider) {
        vscode.workspace.openTextDocument(path.resolve(this.cwd, payload.filepath)).then((document) => {
          vscode.window.showTextDocument(document);
        });
        return;
      }
      const diffTab = vscode.window.tabGroups.all
        .flatMap(group => group.tabs)
        .find(
          tab =>
            tab.input instanceof vscode.TabInputTextDiff
            && tab.input?.original?.scheme === DIFF_VIEW_URI_SCHEME
            && arePathsEqual(tab.input.original.fsPath, payload.filepath),
        );
      if (diffTab?.isActive) {
        return;
      }
      // 激活这个tab
      diffViewProvider.openDiffEditor();
    });
    this.bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.FILE_EDIT, async (payload?: {
      filename: string;
      modelOutput: string;
      sessionId: string;
      chatId: string;
      applyId: string;
    }) => {
      if (!payload) return undefined;
      await this.writeToFile({
        path: payload.filename,
        content: payload.modelOutput,
      }, {
        chatId: payload.chatId,
        sessionId: payload.sessionId,
        source: "markdown-body",
      });

      return undefined;
    });
  }

  /**
   * 注册导航命令
   */
  private registerNavigationCommands() {
    // 注册命令：下一个diff文件
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.nextDiffFile", () => {
        this.navigateToNextDiffFile("next");
      }),
    );

    // 注册命令：上一个diff文件
    this.context.subscriptions.push(
      vscode.commands.registerCommand("kwaipilot.previousDiffFile", () => {
        this.navigateToNextDiffFile("previous");
      }),
    );
  }

  /**
   * 导航到下一个diff文件
   */
  public async navigateToNextDiffFile(type: "previous" | "next") {
    const currentEditor = vscode.window.activeTextEditor;
    const diffInfo = (currentEditor as any).diffInformation as [any];
    if (!currentEditor || !diffInfo) {
      return;
    }
    const currentFile = diffInfo[0].original?.path;
    const diffViewProvider = this.diffState.get(currentFile)?.diffViewProvider;
    if (!diffViewProvider) {
      return;
    }
    const diffFiles = Array.from(this.diffState.keys());
    const index = diffFiles.indexOf(currentFile);
    const nextDiffViewProvider = this.diffState.get(diffFiles[index + (type === "next" ? 1 : -1) % diffFiles.length])?.diffViewProvider;
    if (!nextDiffViewProvider) {
      return;
    }
    await nextDiffViewProvider.openDiffEditor();
  }

  async abortApply(relPath: string) {
    const state = this.state.get(relPath);
    if (!state) {
      return;
    }
    this.logger.debug(`取消${relPath}请求`, this.loggerScope);
    state.requestController?.abort();
  }

  /**
   * 根据 LLM 返回的修改文件的请求，生成最终的文件内容并写入
   * @param payload
   * @param context
   * @returns
   */
  public async writeToFile(payload: EditFileRequest, context: EditFileContext) {
    const relPath = vscode.workspace.asRelativePath(payload.path);
    await this.abortApply(relPath);

    const deferred = createDeferred<EditFileResponse>();

    const applyId = uuidv4();
    const controller = new AbortController();

    this.state.set(relPath, {
      context,
      messageQueue: undefined,
      deferred,
      requestController: controller,
      applyId,
    });

    let currentFileContent = "";
    try {
      currentFileContent = await fs.readFile(path.resolve(this.cwd, relPath), "utf-8");
    }
    catch (error) {
      // 创建文件
      this.logger.debug(`文件${relPath}不存在，将创建新文件`, this.loggerScope);
    }
    const applyBody = {
      files: [
        {
          filePath: relPath,
          fileContent: currentFileContent.toString(),
        },
      ],
      modelOutput: `${payload.content}`,
      language: payload.language,
      instruction: payload.instructions,
      applyId,
      composer: payload.composer,
      platform: PLUGIN_PLATFORM,
      username: this.getBase(GlobalStateManager).get(GlobalState.USER_INFO)?.name ?? "",
    };
    this.logger.info("开始instant-apply", this.loggerScope, {
      value: applyBody,
    });

    const MAX_WAITTING = 30 * 1000;

    let timerout: NodeJS.Timeout = setTimeout(() => {
      controller.abort();
    }, MAX_WAITTING);

    try {
      controller.signal.onabort = () => {
        this.end(relPath);
        this.logger.debug(`取消${relPath}请求`, this.loggerScope);
        // 还原 diffViewProvider 到初始状态
        setTimeout(async () => {
          await this.rejectFile({ filepaths: [relPath] });
          this.composer.updateFileStatus({ filepaths: [relPath], type: "undo" });
        }, 600);
        deferred.resolve({
          type: "failed",
          content: "取消请求",
        });
      };
      this.api.fetchEventSource("/eapi/kwaipilot/chat/instant/apply", {
        method: "POST",
        body: JSON.stringify(applyBody),
        headers: {
          "Content-type": "application/json;charset=UTF-8",
        },
        signal: controller.signal,
        onmessage: (event: any) => {
          clearTimeout(timerout);
          timerout = setTimeout(() => {
            controller.abort();
          }, MAX_WAITTING);

          try {
            const data = JSON.parse(event.data.toString());
            if (data.type === "content") {
              this.updateMessage(relPath, data.content);
            }
          }
          catch (error) {
            this.logger.debug("解析消息失败", this.loggerScope, { err: error });
          }
        },
        onclose: () => {
          clearTimeout(timerout);
          this.end(relPath);
        },
        onerror: (e: any) => {
          clearTimeout(timerout);
          throw Error(e);
        },
      }).catch((error) => {
        this.logger.debug("请求异常", this.loggerScope, { err: error });
        controller.abort();
        this.end(relPath);
        this.showErrorToUser({
          filePath: relPath,
          errorType: "unknown",
          message: `文件处理发生异常: ${error instanceof Error ? error.message : String(error)}`,
          timestamp: Date.now(),
        });
        deferred.resolve({
          type: "failed",
          content: error instanceof Error ? error.message : String(error),
        });
      });
    }
    catch (error) {
      this.logger.debug("请求异常", this.loggerScope, { err: error });
      controller.abort();
      this.end(relPath);
      this.showErrorToUser({
        filePath: relPath,
        errorType: "unknown",
        message: `文件处理发生异常: ${error instanceof Error ? error.message : String(error)}`,
        timestamp: Date.now(),
      });
      deferred.resolve({
        type: "failed",
        content: error instanceof Error ? error.message : String(error),
      });
    }

    return await deferred.promise;
  }

  private end(filepath: string) {
    const state = this.state.get(filepath);
    const messageQueue = state?.messageQueue;
    if (messageQueue) {
      this.logger.debug(`文件处理结束: ${filepath}`, this.loggerScope, {
        value: {
          queueLength: this.fileProcessingQueue.length,
          currentQueue: JSON.stringify(this.fileProcessingQueue),
        },
      });
      messageQueue.end();
      this.state.delete(filepath);
    }
  }

  private updateMessage(filepath: string, message: string) {
    const state = this.state.get(filepath);
    if (!state) {
      throw new Error(`未找到${filepath}对应的state`);
    }
    if (!state.messageQueue) {
      const deferred = state.deferred;
      if (!deferred) {
        this.logger.warn(`未找到${filepath}对应的deferred对象`, this.loggerScope);
        return;
      }

      this.logger.debug(`创建新的消息队列: ${filepath}`, this.loggerScope);
      const messageQueue = new MessageQueue(filepath, this, deferred, state, () => {
        const diffState = this.diffState.get(filepath);
        if (diffState) {
          diffState.diffViewProvider.processLineNumber = 0;
        }
      });

      state.messageQueue = messageQueue;
    }
    state.messageQueue.push(message);
  }

  public async editFile(payload: WriteToFilePayload): Promise<EditFileResponse> {
    const { path: relPath, content, diff, isFinal } = payload;

    if (!relPath || (!content && !diff)) {
      this.logger.warn("收到无效的 WRITE_TO_FILE 事件", this.loggerScope, { value: payload });
      return {
        type: "failed",
        content: `relPath or content or diff is not ok.`,
      };
    }
    if (!this.diffState.has(relPath)) {
      const state = this.state.get(relPath);
      if (state) {
        this.diffState.set(relPath, {
          context: state.context,
          applyId: state.applyId,
          diffViewProvider: new DiffViewProvider(this.cwd),
        });
      }
      else {
        this.diffState.set(relPath, {
          context: {
            sessionId: "inline-chat",
            chatId: "inline-chat",
            source: "inline-chat",
          },
          applyId: "inline-chat",
          diffViewProvider: new DiffViewProvider(this.cwd),
        });
      }
    }
    const diffViewProvider = this.diffState.get(relPath)?.diffViewProvider;

    if (!diffViewProvider) {
      this.logger.warn("收到无效的 WRITE_TO_FILE 事件", this.loggerScope, { value: payload });
      return {
        type: "failed",
        content: `relPath or content or diff is not ok.`,
      };
    }

    // 更新导航按钮状态
    this.updateDiffNavigationContext();

    // Check if file exists using cached map or fs.access
    let fileExists: boolean;
    let oldContent: string;
    if (diffViewProvider.editType !== undefined) {
      fileExists = diffViewProvider.editType === "modify";
    }
    else {
      const absolutePath = path.resolve(this.cwd, relPath);
      fileExists = await fileExistsAtPath(absolutePath);
      diffViewProvider.editType = fileExists ? "modify" : "create";
    }
    if (fileExists) {
      try {
        oldContent = await fs.readFile(path.resolve(this.cwd, relPath), "utf-8");
      }
      catch (error) {
        oldContent = "";
      }
    }
    else {
      oldContent = "";
    }
    try {
      let newContent: string = "";
      if (diff) {
        try {
          newContent = await this.diff.constructNewFileContent(
            diff,
            oldContent,
            isFinal ?? false,
          );
        }
        catch (error) {
          await diffViewProvider.revertChanges();
          await diffViewProvider.reset();
          return {
            type: "failed",
            content: JSON.stringify(error),
          };
        }
      }
      else if (content) {
        newContent = content;

        // pre-processing newContent for cases where weaker models might add artifacts like markdown codeblock markers (deepseek/llama) or extra escape characters (gemini)
        if (newContent.startsWith("```")) {
          // this handles cases where it includes language specifiers like ```python ```js
          newContent = newContent.split("\n").slice(1).join("\n").trim();
        }
        if (newContent.endsWith("```")) {
          newContent = newContent.split("\n").slice(0, -1).join("\n").trim();
        }
      }
      else {
        return {
          type: "failed",
          content: "content is not ok.",
        };
      }

      newContent = newContent.trimEnd(); // remove any trailing newlines, since it's automatically inserted by the editor
      newContent = fixModelHtmlEscaping(newContent);
      newContent = removeInvalidChars(newContent);
      if (!diffViewProvider.isEditing) {
        await diffViewProvider.open(relPath);
      }
      await diffViewProvider.update(newContent, isFinal ?? false);
    }

    catch (error) {
      await diffViewProvider.revertChanges();
      await diffViewProvider.reset();
      return {
        type: "failed",
        content: JSON.stringify(error),
      };
    }
    return {
      type: "success",
      content: `The content was successfully saved to ${relPath.toPosix()}.\n\n`,
    };
  }

  public async finishEditFileWhenEmpty({ path }: {
    path: string;
  }): Promise<EditFileResponse> {
    const diffViewProvider = this.diffState.get(path)?.diffViewProvider;

    if (!diffViewProvider) {
      this.logger.warn("收到无效的 WRITE_TO_FILE 事件", this.loggerScope, { value: path });
      return {
        type: "failed",
        content: `relPath or content or diff is not ok.`,
      };
    }
    if (!diffViewProvider.isEditing) {
      await diffViewProvider.open(path);
    }
    await diffViewProvider.update("", true);

    return {
      type: "success",
      content: `The content was successfully saved to ${path.toPosix()}.\n\n`,
    };
  }

  public async acceptFile({ filepaths, single = true }: { filepaths: string[]; single?: boolean }) {
    for (const filepath of filepaths) {
      const diffViewProvider = this.diffState.get(filepath)?.diffViewProvider;
      if (diffViewProvider?.diffEnded) {
        const diffContent = diffViewProvider.getDiffContents();
        const applyId = this.state.get(filepath)?.applyId ?? "";
        this.api.reportEditFile({
          filepath,
          applyId,
          diffContent: diffContent,
          type: single ? "single" : "all",
        });
        await diffViewProvider.saveChanges();
        this.reportFileApply({
          filename: filepath,
          type: "accept",
        });
        this.diffState.delete(filepath);
      }
    }
    this.reportFileAction({
      filename: filepaths[0],
      type: single ? "single" : "all",
      action: "keep",
    });
    // 更新导航按钮状态
    this.updateDiffNavigationContext();
  }

  public async acceptAll() {
    const diffEndFiles = this.getDiffEndFiles();
    await this.acceptFile({ filepaths: diffEndFiles, single: false });
  }

  public async rejectFile({ filepaths, single = true }: { filepaths: string[]; single?: boolean }) {
    for (const filepath of filepaths) {
      const diffViewProvider = this.diffState.get(filepath)?.diffViewProvider;
      if (diffViewProvider?.diffEnded) {
        await diffViewProvider.revertChanges();
        this.reportFileApply({
          filename: filepath,
          type: "reject",
        });
        this.diffState.delete(filepath);
      }
    }
    // 上报点击事件
    this.reportFileAction({
      filename: filepaths[0],
      type: single ? "single" : "all",
      action: "undo",
    });
    // 更新导航按钮状态
    this.updateDiffNavigationContext();
  }

  public async rejectAll() {
    const diffEndFiles = this.getDiffEndFiles();
    await this.rejectFile({ filepaths: diffEndFiles, single: false });
  }

  private getDiffEndFiles() {
    const filepaths = Array.from(this.diffState.keys());
    return filepaths.filter((filepath) => {
      const diffViewProvider = this.diffState.get(filepath)?.diffViewProvider;
      return diffViewProvider?.diffEnded;
    });
  }

  private get bridge() {
    return this.getBase(Bridge);
  }

  private get diff() {
    return this.getCore(DiffModule);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get api() {
    return this.getBase(Api);
  }

  private get composer() {
    return this.getService(ComposerService);
  }

  /**
   * 更新diff导航按钮的上下文状态
   */
  private updateDiffNavigationContext() {
    const filepaths = Array.from(this.diffState.keys());

    vscode.commands.executeCommand("setContext", "kwaipilot.hasMutipleDiffFiles", filepaths.length > 1);
  }

  /**
   * 向用户显示文件处理错误
   */
  private showErrorToUser(error: FileProcessError) {
    const existingError = this.recentErrors.get(error.filePath);
    if (existingError && existingError.errorType === error.errorType
      && (error.timestamp - existingError.timestamp) < 5000) {
      return; // 5秒内不重复显示相同类型的错误
    }

    this.recentErrors.set(error.filePath, error);

    setTimeout(() => {
      if (this.recentErrors.get(error.filePath) === error) {
        this.recentErrors.delete(error.filePath);
      }
    }, 10000); // 10秒后清理

    // 显示错误提示
    const fileName = path.basename(error.filePath);
    vscode.window.showErrorMessage(`文件 ${fileName} 处理出错: ${error.message}`);
  }

  private reportFileApply({
    filename,
    type,
  }: {
    filename: string;
    type: "accept" | "reject";
  }) {
    const state = this.diffState.get(filename);
    if (!state) return;
    const { applyId, context: { sessionId, chatId } } = state;
    this.getBase(WebloggerManager)?.$reportUserAction<"instant_apply_feedback">({
      key: "instant_apply_feedback",
      sessionId,
      operator: "",
      type,
      subType: "",
      chatId,
      applyId,
    });
  };

  /**
   *
   * @param param0
   */
  private reportFileAction({ filename, action, type }: { filename: string; action: "keep" | "undo"; type: "all" | "single" }) {
    const state = this.diffState.get(filename);
    if (!state) return;
    const { applyId, context: { sessionId, chatId } } = state;
    let param: ReportOpt<"agent_code_keep" | "agent_code_undo">;
    if (action === "keep") {
      param = {
        key: "agent_code_keep",
        type,
      };
      this.getBase(WebloggerManager)?.$reportUserAction(param);
    }
    else {
      param = {
        key: "agent_code_undo",
        type,
      };
    }
    this.getBase(WebloggerManager)?.$reportUserAction({
      ...param,
      chatId,
      applyId,
      sessionId,
    });
  }
}

class MessageQueue {
  private message: string = ""; // 存储接收到的所有消息
  private _isEnd: boolean = false; // 是否结束
  aborted: boolean = false;
  private firstLine = false;
  private timer: any;

  abort() {
    this.aborted = true;
  }

  constructor(
    private filePath: string,
    private writeToFileService: WriteToFileService,
    private editorDeferred: Deferred<EditFileResponse>,
    private state: EditFileState,
    private clear: () => void,
  ) {
    const done = (res: EditFileResponse) => {
      this.clear();
      this.editorDeferred.resolve(res);
    };
    const executeLater = () => {
      clearTimeout(this.timer);
      this.timer = setTimeout(async () => {
        const { content, isEnd } = this.getFullContent();
        if (content) {
          try {
            const res = await this.writeToFileService.editFile({
              path: this.filePath,
              content: content,
              isFinal: isEnd,
            });
            if (isEnd) {
              done(res);
            }
            else {
              executeLater();
            }
          }
          catch (e) {
            done({
              type: "failed",
              content: `editFile execution failed ${String(e)} path:${this.filePath.toPosix()}.\n\n`,
            });
            return;
          }
        }
        else if (isEnd) {
          const res = await this.writeToFileService.finishEditFileWhenEmpty({
            path: this.filePath,
          });
          done(res);
        }
        else {
          executeLater();
        }
      }, 400);
    };

    executeLater();
  }

  private async writeToFile(content: string) {
    return await this.writeToFileService.editFile({
      path: this.filePath,
      content: content,
      isFinal: this._isEnd,
    });
  }

  push(message: string): void {
    // 添加消息到数组
    this.message += message;
  }

  async end() {
    this.message += "IrMOxqB41rxZRyrKzGepm";
  }

  /**
   * 获取完整的消息内容
   */
  private getFullContent(): {
    content: string;
    isEnd: boolean;
  } {
    if (this.message.includes("IrMOxqB41rxZRyrKzGepm")) {
      return {
        content: this.message.split("\n").slice(0, -1).join("\n"),
        isEnd: true,
      };
    }
    if (!this.message || this.message.trim().length === 0 || !this.message.includes("\n")) {
      return {
        content: "",
        isEnd: false,
      };
    }
    const lines = this.message.split("\n");
    let startIndex = 0;
    if (!this.firstLine) {
      startIndex = 1;
      this.firstLine = true;
    };
    this.message = lines[lines.length - 1];
    return {
      content: lines.slice(startIndex, -1).join("\n"),
      isEnd: this.message.includes("IrMOxqB41rxZRyrKzGepm"),
    };
  }

  /**
   * 获取文件路径
   */
  get filename(): string {
    return this.filePath;
  }
}
