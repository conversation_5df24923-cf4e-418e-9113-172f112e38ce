import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { Webview } from "../../base/webview";
import { WebloggerManager } from "../../base/weblogger";
import { LoggerManager } from "../../base/logger";
import { GlobalStateManager } from "../../base/state-manager";
import * as vscode from "vscode";
import { GlobalState } from "../../base/state-manager/types";
import { Project } from "../../core/project";
import { setupInlineTips } from "./tips";
import { InlineChatInfo } from "../../shared/types";
import { DiffModule } from "../../core/diff";
import { Bridge } from "../../base/bridge";
import { NATIVE_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { WriteToFileService } from "../write-to-file";
import { InlineChatMessageEndTag } from "../../shared/constant";

import fs from "fs/promises";

// 当前活跃的消息处理器
let activeMessageProcessor: MessageProcessor | null = null;

export class InlineChatService extends ServiceModule {
  constructor(ext: ContextManager) {
    super(ext);
    this.globalState.update(GlobalState.INLINE_CHAT_INFO, undefined);
    this.registryListener(ext.context);
    setupInlineTips(ext.context);
  }

  registryListener(context: vscode.ExtensionContext) {
    context.subscriptions.push(this.getDisposable());
    context.subscriptions.push(this.getDisposableInlineChatFileChange(context));
    context.subscriptions.push(
      vscode.commands.registerCommand(
        "kwaipilot.inlineChat",
        (param?: InlineChatInfo) => this.handleInlineChat(param),
      ),
    );

    // 监听消息事件
    this.bridge.on(
      NATIVE_BRIDGE_EVENT_NAME.STREAM_DIFF_MESSAGE,
      ({ message, _autoCreate, _autoOpen }) => {
        const inlineChatInfo = this.globalState.get(
          GlobalState.INLINE_CHAT_INFO,
        );
        if (!inlineChatInfo) {
          return;
        }

        // 如果没有活跃的处理器，创建一个新的
        if (!activeMessageProcessor) {
          activeMessageProcessor = new MessageProcessor(
            inlineChatInfo.filepath,
            this.getService(WriteToFileService),
            inlineChatInfo,
          );
        }

        // 如果是结束标记，标记处理完成
        if (message.includes(InlineChatMessageEndTag)) {
          if (activeMessageProcessor) {
            activeMessageProcessor.markAsComplete();
          }
          return;
        }

        // 添加消息到处理器
        activeMessageProcessor.addMessage(message);
      },
    );
  }

  getDisposable() {
    return vscode.window.onDidChangeActiveTextEditor((editor) => {
      const inlineChatInfo = this.globalState.get(GlobalState.INLINE_CHAT_INFO);
      if (!inlineChatInfo) {
        return;
      }

      // 检查是否所有编辑器都已关闭
      if (!editor) {
        if (this.project.getOpenTabsCount() === 0) {
          this.cleanupInlineChat();
        }
        return;
      }
      if (editor.document.uri.scheme === "output") {
        return;
      }
      if (editor.document.uri.path !== inlineChatInfo.filepath) {
        this.logger.info("active file change", "inline-chat");
        const initialInlineChatInfo: InlineChatInfo = {
          filepath: editor.document.uri.path,
          startLine: 1,
          endLine: 1,
          content: "",
        };
        this.globalState.update(
          GlobalState.INLINE_CHAT_INFO,
          initialInlineChatInfo,
        );

        // 只在插件面板可见时更新视图数据
        if (this.webview._view?.visible) {
          this.bridge.setInlineChat(initialInlineChatInfo);
        }
      }
    });
  }

  private cleanupInlineChat() {
    // 清理当前处理器
    activeMessageProcessor = null;
    this.globalState.update(GlobalState.INLINE_CHAT_INFO, undefined);
    this.bridge.setInlineChat(undefined);
  }

  getDisposableInlineChatFileChange(context: vscode.ExtensionContext) {
    /** inline chat时行号变化 */
    return vscode.workspace.onDidChangeTextDocument((event) => {
      const inlineChatInfo = this.globalState.get(GlobalState.INLINE_CHAT_INFO);
      if (!inlineChatInfo) {
        return;
      }

      if (event.document.uri.path === inlineChatInfo.filepath) {
        const change = event.contentChanges;
        let contextEndline = inlineChatInfo.endLine;
        for (const c of change) {
          const startIndex = c.range.start.line;

          const endIndex = c.range.end.line;
          const text = c.text;
          if (
            startIndex + 1 >= inlineChatInfo.startLine
            && startIndex + 1 <= contextEndline
          ) {
            // 删除
            if (text === "") {
              const deleteLineNumber = endIndex - startIndex;
              contextEndline = contextEndline - deleteLineNumber;
              if (contextEndline >= inlineChatInfo.startLine) {
                context.globalState.update("inlineChatInfo", {
                  ...inlineChatInfo,
                  endLine: contextEndline,
                });
              }
            }
            // 新增
            if (endIndex === startIndex) {
              const addLineNumber = text.split("\n").length - 1;
              contextEndline = contextEndline + addLineNumber;
              context.globalState.update("inlineChatInfo", {
                ...inlineChatInfo,
                endLine: contextEndline,
              });
            }
          }
        }
      }
    });
  }

  async handleInlineChat(param?: InlineChatInfo) {
    const userInfo = this.globalState.get(GlobalState.USER_INFO);
    if (!userInfo) {
      vscode.window.showErrorMessage("Kwaipilot：请在登录后使用相关功能！");
      return;
    }
    vscode.workspace
      .getConfiguration("kwaipilot")
      .update(
        "settings.showInlineTip",
        false,
        vscode.ConfigurationTarget.Global,
      );
    if (!param?.startLine) {
      // NOTE: pram 为空时，快捷键触发， param.content为空时，右键触发
      this.webview.focus("key_binding");
      this.weblogger.$reportUserAction({
        key: "inline_chat",
        type: "window_show",
      });
      const editor = vscode.window.activeTextEditor;
      if (editor) {
        const selection = editor.selection;
        const text = editor.document.getText(selection);
        // NOTE: 这里行号是从0️⃣开始的，所以需要+1
        const startIndex = selection.start.line;
        const startLine = startIndex + 1;
        const endIndex = selection.end.line;
        const endLine = endIndex + 1;
        const filePath = editor.document.uri.fsPath;
        // 前闭后开,所以 suffix是从 endIndex + 1开始
        const prefix = editor.document.getText(
          new vscode.Range(
            new vscode.Position(0, 0),
            new vscode.Position(startIndex, 0),
          ),
        );
        const suffix = editor.document.getText(
          new vscode.Range(
            new vscode.Position(endIndex + 1, 0),
            new vscode.Position(editor.document.lineCount, 0),
          ),
        );
        const inlineChatInfo = {
          filepath: filePath,
          content: text,
          startLine,
          endLine,
          prefix,
          suffix,
        };
        this.bridge.setInlineChat(inlineChatInfo);
        this.globalState.update(GlobalState.INLINE_CHAT_INFO, inlineChatInfo);
        // 清理上一个会话的处理器
        activeMessageProcessor = null;
      }
    }
    else {
      const inlineChatInfo = {
        filepath: param.filepath,
        content: param.content,
        startLine: param.startLine,
        endLine: param.endLine,
        prefix: param.prefix,
        suffix: param.suffix,
      };
      this.globalState.update(GlobalState.INLINE_CHAT_INFO, inlineChatInfo);
      this.bridge.setInlineChat(inlineChatInfo);
      // 清理上一个会话的处理器
      activeMessageProcessor = null;
    }
  }

  private get webview() {
    return this.getBase(Webview);
  }

  private get weblogger() {
    return this.getBase(WebloggerManager);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  private get globalState() {
    return this.getBase(GlobalStateManager);
  }

  private get project() {
    return this.getCore(Project);
  }

  private get diff() {
    return this.getCore(DiffModule);
  }

  private get bridge() {
    return this.getBase(Bridge);
  }
}

/**
 * 消息处理器 - 处理单个文件的内联聊天消息
 */
class MessageProcessor {
  private message: string = "";
  private isComplete: boolean = false;
  private prefix: string = "";
  private suffix: string = "";
  private isInitialized: boolean = false;
  private updateTimer: NodeJS.Timeout | null = null;
  private retryCount: number = 0;
  private readonly MAX_RETRIES = 3;
  private readonly RETRY_DELAY = 1000; // 1秒
  private readonly UPDATE_INTERVAL = 100; // 更新间隔

  constructor(
    private filePath: string,
    private writeToFileService: WriteToFileService,
    private inlineChatInfo: InlineChatInfo,
  ) {
    this.initialize();
  }

  /**
   * 初始化前缀和后缀
   */
  private async initialize() {
    try {
      // 从InlineChatInfo读取前缀和后缀
      if (this.inlineChatInfo.prefix && this.inlineChatInfo.suffix) {
        this.prefix = this.inlineChatInfo.prefix;
        this.suffix = this.inlineChatInfo.suffix;
        this.isInitialized = true;
      }
      else {
        // 从文件中读取
        const content = await fs.readFile(this.filePath, "utf-8");
        const lines = content.split("\n");
        this.prefix = lines.slice(0, this.inlineChatInfo.startLine - 1).join("\n");
        this.suffix = lines.slice(this.inlineChatInfo.endLine).join("\n");
        this.isInitialized = true;
      }

      // 开始更新循环
      this.startUpdateLoop();
    }
    catch (error) {
      console.error("初始化内联聊天消息处理器失败", error);
    }
  }

  /**
   * 添加消息
   */
  public addMessage(message: string) {
    this.message += message;
  }

  /**
   * 标记处理完成
   */
  public markAsComplete() {
    this.isComplete = true;
  }

  /**
   * 开始更新循环
   */
  private startUpdateLoop() {
    // 清理上一个定时器
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
    }

    // 设置更新循环
    this.updateTimer = setInterval(() => {
      this.updateFile();
    }, this.UPDATE_INTERVAL);
  }

  /**
   * 更新文件
   */
  private async updateFile() {
    // 如果初始化未完成，跳过
    if (!this.isInitialized) {
      return;
    }

    try {
      const content = this.getFullContent();
      if (!content || content.trim().length === 0) {
        return;
      }

      // 获取相对路径
      const relPath = vscode.workspace.asRelativePath(this.filePath);

      // 写入文件
      await this.writeToFileService.editFile({
        path: relPath,
        content: content,
        isFinal: this.isComplete,
      });

      // 如果处理完成，清理更新循环
      if (this.isComplete) {
        this.cleanup();
      }

      // 重置重试计数
      this.retryCount = 0;
    }
    catch (error) {
      this.retryCount++;
      console.error(`更新文件失败 (${this.retryCount}/${this.MAX_RETRIES})`, error);

      // 如果超过最大重试次数，清理
      if (this.retryCount >= this.MAX_RETRIES) {
        this.cleanup();
      }
    }
  }

  /**
   * 清理资源
   */
  private cleanup() {
    if (this.updateTimer) {
      clearInterval(this.updateTimer);
      this.updateTimer = null;
    }

    // 如果有全局引用，释放它
    if (activeMessageProcessor === this) {
      activeMessageProcessor = null;
    }
  }

  /**
   * 获取完整的内容
   */
  private getFullContent(): string {
    if (!this.message || this.message.trim().length === 0) {
      return "";
    }

    try {
      // 处理消息内容
      let processedContent = this.message;

      // 如果有第一行，可能是代码块标识符，移除它
      if (processedContent.includes("\n")) {
        const lines = processedContent.split("\n");
        if (lines[0].startsWith("```")) {
          processedContent = lines.slice(1).join("\n");
        }
      }

      // 删除结尾的代码块标记
      processedContent = processedContent.replace(/```$/g, "");

      // 修剪内容
      processedContent = processedContent.trim();

      // 根据是否完成决定是否添加后缀
      if (this.isComplete) {
        return `${this.prefix}\n${processedContent}\n${this.suffix}`;
      }
      else {
        return `${this.prefix}\n${processedContent}`;
      }
    }
    catch (error) {
      console.error("处理消息内容出错", error);
      return this.message;
    }
  }
}
