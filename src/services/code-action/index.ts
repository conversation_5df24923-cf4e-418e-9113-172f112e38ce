import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import { Webview } from "../../base/webview";
import * as vscode from "vscode";
import { ActionType, CodeSection } from "../../shared/types";
import { checkLogin } from "../../utils/login";
import { CodeBlockActionProvider } from "./codeLens";
import { LoggerManager } from "../../base/logger";
import { WEBVIEW_BRIDGE_EVENT_NAME } from "../../shared/types/bridge";
import { Bridge } from "../../base/bridge";

export class CodeActionService extends ServiceModule {
  private commandMap: Record<string, string> = {
    "kwaipilot.generateFunctionComment": "functionComment",
    "kwaipilot.generateLineComment": "lineComment",
    "kwaipilot.generateCodeExplanation": "codeExplanation",
    "kwaipilot.generateFunctionSplit": "functionSplit",
    "kwaipilot.generateTuningSuggestion": "tuningSuggestion",
  };

  private codeBlockActionProvider: CodeBlockActionProvider;
  private readonly loggerScope = "code-action:codeLens";
  constructor(ext: ContextManager) {
    super(ext);
    this.codeBlockActionProvider = new CodeBlockActionProvider(ext);
    this.registerCommands(ext.context);
  }

  registerCommands(
    context: vscode.ExtensionContext,
  ) {
    // 注册代码块事件
    const onCommandsGenerateCommentMessage = vscode.commands.registerCommand(
      "kwaipilot.generateFunctionComment",
      (section: CodeSection, fullPath: string) => {
        this.executeCodeAction(
          "kwaipilot.generateFunctionComment",
          section,
          fullPath,
        );
      },
    );
    const onCommandsGenerateLineComment = vscode.commands.registerCommand(
      "kwaipilot.generateLineComment",
      (section: CodeSection, fullPath: string) => {
        this.executeCodeAction(
          "kwaipilot.generateLineComment",
          section,
          fullPath,
        );
      },
    );
    const onCommandsGenerateCodeExplanation = vscode.commands.registerCommand(
      "kwaipilot.generateCodeExplanation",
      (section: CodeSection, fullPath: string) => {
        this.executeCodeAction(
          "kwaipilot.generateCodeExplanation",
          section,
          fullPath,
        );
      },
    );
    const onCommandsGenerateFunctionSplit = vscode.commands.registerCommand(
      "kwaipilot.generateFunctionSplit",
      (section: CodeSection, fullPath: string) => {
        this.executeCodeAction(
          "kwaipilot.generateFunctionSplit",
          section,
          fullPath,
        );
      },
    );
    const onCommandsGenerateTuningSuggestion = vscode.commands.registerCommand(
      "kwaipilot.generateTuningSuggestion",
      (section: CodeSection, fullPath: string) => {
        this.executeCodeAction(
          "kwaipilot.generateTuningSuggestion",
          section,
          fullPath,
        );
      },
    );
    context.subscriptions.push(
      onCommandsGenerateCommentMessage,
      onCommandsGenerateLineComment,
      onCommandsGenerateCodeExplanation,
      onCommandsGenerateFunctionSplit,
      onCommandsGenerateTuningSuggestion,
    );

    let debounceTimer: NodeJS.Timeout | null = null;
    const clearTimeoutDisposable: vscode.Disposable = {
      dispose() {
        if (debounceTimer) {
          clearTimeout(debounceTimer);
        }
      },
    };

    const disposables = [
      vscode.languages.registerCodeLensProvider("*", this.codeBlockActionProvider),
      vscode.commands.registerCommand("kwaipilot.codeBlockAction", async () => {
        checkLogin(context);
        await vscode.workspace
          .getConfiguration("kwaipilot")
          .update("settings.codeBlockAction", true, true);
        // commands.executeCommand('codeLens.refresh');
      }),
      vscode.commands.registerCommand("kwaipilot.disableCodeMark", () => {
        vscode.workspace
          .getConfiguration("kwaipilot")
          .update("settings.codeBlockAction", false, true);
      }),
      vscode.workspace.onDidChangeTextDocument((event) => {
        // 清除之前的定时器
        if (debounceTimer) {
          clearTimeout(debounceTimer);
        }

        // 设置新的定时器，延迟处理文件变动事件
        debounceTimer = setTimeout(() => {
          // 检查文件变动是否是用户编辑触发的
          if (event.contentChanges.length > 0 && event.document.isDirty) {
            // 文件内容发生变动且未保存
            // commands.executeCommand('codeLens.refresh');
          }
        }, 10 * 1000);
      }),
      clearTimeoutDisposable,
    ];

    return disposables;
  }

  executeCodeAction(command: string, section: CodeSection, fullPath: string) {
    if (!section.content) {
      // 理论上不会出现
      return;
    }
    // 上报埋点
    const action = this.commandMap[command] || "";
    if (!action) {
      this.logger.error("command not found by CodeActionService", this.loggerScope, { value: action });
      throw new Error("command not found by CodeActionService");
    }
    const editor = vscode.window.activeTextEditor;
    const languageId = editor?.document.languageId ?? "";
    this.webview.focus("code_action");
    const sidePanelWebview = this.getBase(Webview)._view?.webview;
    if (!sidePanelWebview) {
      throw new Error("side panel webview is not ready");
    }
    this.getBase(Bridge).callHandler(sidePanelWebview, WEBVIEW_BRIDGE_EVENT_NAME.ACTION_FOR_CODE, {
      type: action as ActionType,
      selectText: section.content,
      languageId,
      section,
      fullPath,
    });
  }

  private get webview() {
    return this.getBase(Webview);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }
}
