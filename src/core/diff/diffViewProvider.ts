import * as vscode from "vscode";
import * as path from "path";
import * as fs from "fs/promises";
import { createDirectoriesForFile } from "../../utils/fs";
import { arePathsEqual } from "../../utils/path";
import { DecorationController } from "./decoration";
import * as diff from "diff";
import { DiffBlock } from "../../base/http-client/interface";

export const DIFF_VIEW_URI_SCHEME = "kwaipilot-diff";

export class DiffViewProvider {
  editType?: "create" | "modify";
  isEditing = false;
  originalContent: string | undefined;
  private createdDirs: string[] = [];
  private documentWasOpen = false;
  private relPath?: string;
  private newContent?: string;
  private activeDiffEditor?: vscode.TextEditor;
  private fadedOverlayController?: DecorationController;
  private activeLineController?: DecorationController;
  private streamedLines: string[] = [];
  private _diffEnded = false;
  processLineNumber = 0;

  constructor(private cwd: string) {}

  async open(relPath: string): Promise<void> {
    this.relPath = relPath;
    const fileExists = this.editType === "modify";
    const absolutePath = path.resolve(this.cwd, relPath);
    this.isEditing = true;
    // if the file is already open, ensure it's not dirty before getting its contents
    if (fileExists) {
      const existingDocument = vscode.workspace.textDocuments.find(doc => arePathsEqual(doc.uri.fsPath, absolutePath));
      if (existingDocument && existingDocument.isDirty) {
        await existingDocument.save();
      }
    }

    if (fileExists) {
      try {
        this.originalContent = await fs.readFile(absolutePath, "utf-8");
      }
      catch (e) {
        this.originalContent = "";
      }
    }
    else {
      this.originalContent = "";
    }
    // for new files, create any necessary directories and keep track of new directories to delete if the user denies the operation
    this.createdDirs = await createDirectoriesForFile(absolutePath);
    // make sure the file exists before we open it
    if (!fileExists) {
      await fs.writeFile(absolutePath, "");
    }
    // if the file was already open, close it (must happen after showing the diff view since if it's the only tab the column will close)
    this.documentWasOpen = false;
    // close the tab if it's open (it's already saved above)
    const tabs = vscode.window.tabGroups.all
      .map(tg => tg.tabs)
      .flat()
      .filter(tab => tab.input instanceof vscode.TabInputText && arePathsEqual(tab.input.uri.fsPath, absolutePath));
    for (const tab of tabs) {
      if (!tab.isDirty) {
        await vscode.window.tabGroups.close(tab);
      }
      this.documentWasOpen = true;
    }

    // 在打开新的diff视图前，先关闭当前文件的任何现有diff视图
    await this.closeCurrentDiffView();

    this.activeDiffEditor = await this.openDiffEditor();
    this.fadedOverlayController = new DecorationController("fadedOverlay", this.activeDiffEditor);
    this.activeLineController = new DecorationController("activeLine", this.activeDiffEditor);
    // 只在编辑区应用蒙层
    if (this.activeDiffEditor) {
      const document = this.activeDiffEditor.document;
      const lastLine = document.lineCount - 1;
      this.fadedOverlayController.addLines(0, lastLine + 1);
    }
    this.scrollEditorToLine(0);
    this.streamedLines = [];
  }

  async update(content: string, isFinal: boolean) {
    if (!this.relPath || !this.activeLineController || !this.fadedOverlayController) {
      throw new Error("Required values not set");
    }
    this.newContent += content;
    const contentLines = content.split("\n");

    const diffEditor = this.activeDiffEditor;
    const document = diffEditor?.document;
    if (!diffEditor || !document) {
      throw new Error("User closed text editor, unable to edit file...");
    }

    // Place cursor at the beginning of the diff editor to keep it out of the way of the stream animation
    const beginningOfDocument = new vscode.Position(this.processLineNumber, 0);
    diffEditor.selection = new vscode.Selection(beginningOfDocument, beginningOfDocument);

    for (let i = 0; i < contentLines.length; i++) {
      const edit = new vscode.WorkspaceEdit();
      const rangeToReplace = new vscode.Range(this.processLineNumber, 0, this.processLineNumber + 1, 0);
      const contentToReplace = contentLines[i] + "\n";
      edit.replace(document.uri, rangeToReplace, contentToReplace);
      await vscode.workspace.applyEdit(edit);
      // Update decorations
      this.activeLineController.setActiveLine(this.processLineNumber);
      this.fadedOverlayController.updateOverlayAfterLine(this.processLineNumber, document.lineCount);
      this.processLineNumber++;
    }
    // Update the streamedLines with the new accumulated content
    this.streamedLines.push(...contentLines);
    if (isFinal) {
      // Handle any remaining lines if the new content is shorter than the original
      if (this.processLineNumber < document.lineCount) {
        const edit = new vscode.WorkspaceEdit();
        edit.delete(document.uri, new vscode.Range(this.processLineNumber, 0, document.lineCount, 0));
        await vscode.workspace.applyEdit(edit);
      }
      // Add empty last line if original content had one
      const hasEmptyLastLine = this.originalContent?.endsWith("\n");
      if (hasEmptyLastLine) {
        const edit = new vscode.WorkspaceEdit();
        edit.replace(document.uri, new vscode.Range(this.processLineNumber + 1, 0, this.processLineNumber + 1, 0), "\n");
      }
      // Clear all decorations at the end (before applying final edit)
      this.fadedOverlayController.clear();
      this.activeLineController.clear();

      // 执行保存操作
      if (diffEditor.document.isDirty) {
        await diffEditor.document.save();
      }
      this._diffEnded = true;
    }
  }

  get diffEnded() {
    return this._diffEnded;
  }

  /**
   * 只关闭当前文件的diff视图
   */
  private async closeCurrentDiffView() {
    if (!this.relPath) {
      return;
    }
    const absolutePath = path.resolve(this.cwd, this.relPath);
    const tabs = vscode.window.tabGroups.all
      .flatMap(tg => tg.tabs)
      .filter(tab =>
        tab.input instanceof vscode.TabInputTextDiff
        && tab.input?.original?.scheme === DIFF_VIEW_URI_SCHEME
        && tab.input.modified
        && arePathsEqual(tab.input.modified.fsPath, absolutePath),
      );

    for (const tab of tabs) {
      // trying to close dirty views results in save popup
      if (!tab.isDirty) {
        await vscode.window.tabGroups.close(tab);
      }
    }
  }

  async saveChanges(): Promise<{
    newProblemsMessage: string | undefined;
    userEdits: string | undefined;
    autoFormattingEdits: string | undefined;
    finalContent: string | undefined;
  }> {
    if (!this.relPath || !this.newContent || !this.activeDiffEditor) {
      return {
        newProblemsMessage: undefined,
        userEdits: undefined,
        autoFormattingEdits: undefined,
        finalContent: undefined,
      };
    }
    const absolutePath = path.resolve(this.cwd, this.relPath);

    // 直接关闭当前diff视图即可，因为文件已经在diff结束时保存了
    try {
      await vscode.window.showTextDocument(vscode.Uri.file(absolutePath), {
        preview: false,
      });
    }
    catch (e) {
      // ignore
    }
    // 只关闭当前文件的diff视图，而不是所有diff视图
    await this.closeCurrentDiffView();
    await this.reset();

    return {
      newProblemsMessage: undefined,
      userEdits: undefined,
      autoFormattingEdits: undefined,
      finalContent: undefined,
    };
  }

  async revertChanges(): Promise<void> {
    if (!this.relPath || !this.activeDiffEditor) {
      return;
    }
    const fileExists = this.editType === "modify";
    const updatedDocument = this.activeDiffEditor.document;
    const absolutePath = path.resolve(this.cwd, this.relPath);

    if (!fileExists) {
      // 如果是新文件，直接删除
      // 只关闭当前文件的diff视图，而不是所有diff视图
      await this.closeCurrentDiffView();
      try {
        await fs.unlink(absolutePath);
      }
      catch (e) {
        // ignore
        console.error(e);
      }
      // Remove only the directories we created, in reverse order
      for (let i = this.createdDirs.length - 1; i >= 0; i--) {
        await fs.rmdir(this.createdDirs[i]);
        console.log(`Directory ${this.createdDirs[i]} has been deleted.`);
      }
      console.log(`File ${absolutePath} has been deleted.`);
    }
    else {
      // 手动还原文件内容到原始状态
      const edit = new vscode.WorkspaceEdit();
      const fullRange = new vscode.Range(
        updatedDocument.positionAt(0),
        updatedDocument.positionAt(updatedDocument.getText().length),
      );
      edit.replace(updatedDocument.uri, fullRange, this.originalContent ?? "");
      await vscode.workspace.applyEdit(edit);

      // 保存更改
      if (updatedDocument.isDirty) {
        await updatedDocument.save();
      }

      if (this.documentWasOpen) {
        await vscode.window.showTextDocument(vscode.Uri.file(absolutePath), {
          preview: false,
        });
      }
      // 只关闭当前文件的diff视图，而不是所有diff视图
      await this.closeCurrentDiffView();
    }

    // edit is done
    await this.reset();
  }

  public async openDiffEditor(): Promise<vscode.TextEditor> {
    if (!this.relPath) {
      throw new Error("No file path set");
    }
    const uri = vscode.Uri.file(path.resolve(this.cwd, this.relPath));

    // 首先尝试查找已存在的diff面板
    const existingDiffTab = await this.findExistingDiffTab(uri);
    if (existingDiffTab && existingDiffTab.input instanceof vscode.TabInputTextDiff) {
      // 如果找到已存在的diff面板，先关闭它
      if (!existingDiffTab.isDirty) {
        await vscode.window.tabGroups.close(existingDiffTab);
      }
    }

    // 创建新的diff面板
    return new Promise<vscode.TextEditor>((resolve, reject) => {
      const fileName = path.basename(uri.fsPath);
      const fileExists = this.editType === "modify";
      const disposable = vscode.window.onDidChangeActiveTextEditor((editor) => {
        if (editor && arePathsEqual(editor.document.uri.fsPath, uri.fsPath)) {
          disposable.dispose();
          vscode.commands.executeCommand("setContext", "kwaipilot.inDiffMode", true);
          resolve(editor);
        }
      });
      vscode.commands.executeCommand(
        "vscode.diff",
        vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${fileName}`).with({
          query: Buffer.from(this.originalContent ?? "").toString("base64"),
        }),
        uri,
        `${fileName}: ${fileExists ? "Original ↔ Kwaipilot's Changes" : "New File"} (Editable)`,
        {
          preview: false, // 设置为false，使diff面板保持固定打开状态
        },
      );
      setTimeout(() => {
        disposable.dispose();
        reject(new Error("Failed to open diff editor, please try again..."));
      }, 10_000);
    });
  }

  /**
   * 查找已存在的diff面板
   * @param uri 文件URI
   * @returns 如果找到已存在的diff面板则返回该面板，否则返回undefined
   */
  private async findExistingDiffTab(uri: vscode.Uri): Promise<vscode.Tab | undefined> {
    return vscode.window.tabGroups.all
      .flatMap(group => group.tabs)
      .find(
        tab =>
          tab.input instanceof vscode.TabInputTextDiff
          && tab.input?.original?.scheme === DIFF_VIEW_URI_SCHEME
          && arePathsEqual(tab.input.modified.fsPath, uri.fsPath),
      );
  }

  /**
   * 切换到指定文件的diff面板，如果不存在则创建新的
   * @param relPath 相对路径
   */
  public async switchToDiffView(relPath: string): Promise<void> {
    this.relPath = relPath;
    const uri = vscode.Uri.file(path.resolve(this.cwd, relPath));

    // 查找已存在的diff面板
    const existingDiffTab = await this.findExistingDiffTab(uri);
    if (existingDiffTab && existingDiffTab.input instanceof vscode.TabInputTextDiff) {
      // 如果找到已存在的diff面板，先关闭它
      if (!existingDiffTab.isDirty) {
        await vscode.window.tabGroups.close(existingDiffTab);
      }
    }

    // 创建新的diff面板
    const fileName = path.basename(uri.fsPath);
    const fileExists = this.editType === "modify";
    await vscode.commands.executeCommand(
      "vscode.diff",
      vscode.Uri.parse(`${DIFF_VIEW_URI_SCHEME}:${fileName}`).with({
        query: Buffer.from(this.originalContent ?? "").toString("base64"),
      }),
      uri,
      `${fileName}: ${fileExists ? "Original ↔ Kwaipilot's Changes" : "New File"} (Editable)`,
      {
        preview: false, // 设置为false，使diff面板保持固定打开状态
      },
    );
  }

  private scrollEditorToLine(_line: number) {
    if (this.activeDiffEditor) {
      return;
    }
  }

  scrollToFirstDiff() {
    if (!this.activeDiffEditor) {
      return;
    }
    const currentContent = this.activeDiffEditor.document.getText();
    const diffs = diff.diffLines(this.originalContent || "", currentContent);
    let lineCount = 0;
    for (const part of diffs) {
      if (part.added || part.removed) {
        // Found the first diff, scroll to it
        this.activeDiffEditor.revealRange(
          new vscode.Range(lineCount, 0, lineCount, 0),
          vscode.TextEditorRevealType.InCenter,
        );
        return;
      }
      if (!part.removed) {
        lineCount += part.count || 0;
      }
    }
  }

  // close editor if open?
  async reset() {
    this.editType = undefined;
    this.isEditing = false;
    this.originalContent = undefined;
    this.createdDirs = [];
    this.documentWasOpen = false;
    this.activeDiffEditor = undefined;
    this.fadedOverlayController = undefined;
    this.activeLineController = undefined;
    this.streamedLines = [];

    // 清除diff模式上下文变量
    vscode.commands.executeCommand("setContext", "kwaipilot.inDiffMode", false);
  }

  getDiffContents() {
    if (!this.activeDiffEditor || this.originalContent === undefined) {
      return [];
    }

    const result: DiffBlock[] = [];

    try {
      const diffInformation = (this.activeDiffEditor as any).diffInformation as {
        changes: { original: {
          startLineNumber: number;
          endLineNumberExclusive: number;
        };
        modified: {
          startLineNumber: number;
          endLineNumberExclusive: number;
        };
        }[];
        documentVersion: number;
        modified: vscode.Uri;
        original: vscode.Uri;
      }[];

      // 添加保护检查，确保diffInformation存在且有效
      if (!diffInformation || !Array.isArray(diffInformation) || diffInformation.length === 0) {
        console.warn("getDiffContents: diffInformation is invalid or empty");
        return [];
      }

      // 检查diffInformation[0]和changes是否存在
      if (!diffInformation[0] || !diffInformation[0].changes || !Array.isArray(diffInformation[0].changes)) {
        console.warn("getDiffContents: diffInformation structure is not as expected");
        return [];
      }

      const diffBlocks = diffInformation[0].changes;
      const originContentLines = this.originalContent.split("\n");
      for (const block of diffBlocks) {
        // 检查block是否包含必要的属性
        if (!block.original || !block.modified) {
          continue; // 跳过无效的block
        }

        const originStartLine = block.original.startLineNumber;
        const originEndLine = block.original.endLineNumberExclusive;
        const modifyStartLine = block.modified.startLineNumber;
        const modifyEndLine = block.modified.endLineNumberExclusive;

        // 确保所有行号都是有效的数字
        if (isNaN(originStartLine) || isNaN(originEndLine)
          || isNaN(modifyStartLine) || isNaN(modifyEndLine)) {
          continue; // 跳过无效的行号
        }

        const originContent = originContentLines.slice(originStartLine, originEndLine).join("\n");

        // 使用try-catch防止document.getText出错
        let modifyContent = "";
        try {
          modifyContent = this.activeDiffEditor.document.getText(
            new vscode.Range(modifyStartLine - 1, 0, modifyEndLine - 1, 0),
          );
        }
        catch (error) {
          console.error("Error getting modified text:", error);
          continue; // 跳过出错的块
        }

        result.push({
          originStartLine,
          originEndLine,
          originContent,
          modifyStartLine,
          modifyEndLine,
          modifyContent,
        });
      }
    }
    catch (error) {
      console.error("Error in getDiffContents:", error);
      // 发生任何错误时，返回空数组而不是中断执行
    }

    return result;
  }
}
