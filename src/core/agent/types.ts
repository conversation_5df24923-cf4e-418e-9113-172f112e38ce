export type IProtocol = Record<string, [any, any]>;

export interface IdeCommonMessage {
  pluginVersion: string;
  version: string;
  platform: IdePlatform;
  cwd: string;
  repo: {
    git_url: string;
    dir_path: string;
    commit: string;
  };
}
export interface AgentCommonMessage {
  version: string;
}

export interface IdeSettings {
  dirPath: string; // 工作目录
  fileRetryTime: number; // 索引构建失败几次算失败
  modelRetryTime: number; // 调用模型多少次失败算失败
  enableRepoIndex: boolean; // 是否开启仓库索引
  maxIndexSpace: number; // 最大索引空间大小（单位：GB）
  proxyUrl: string; // 代理地址
}

export interface IdeInfo {
  version: string;
  maxIndexSpace: number; // 最大索引空间大小（单位：GB）
}
export type IdePlatform = "xcode" | "vscode" | "jetbrains";

export interface Message<T = any> {
  common?: IdeCommonMessage | AgentCommonMessage;
  messageType: string;
  messageId: string;
  data: T;
}
export interface IMessenger<ToProtocol extends IProtocol, FromProtocol extends IProtocol> {
  onError(handler: (error: Error, details?: string) => void): void;
  send<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0], messageId?: string): string;

  on<T extends keyof ToProtocol>(
    messageType: T,
    handler: (message: Message<ToProtocol[T][0]>) => Promise<ToProtocol[T][1]> | ToProtocol[T][1]
  ): void;

  request<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0]): Promise<FromProtocol[T][1]>;

  invoke<T extends keyof ToProtocol>(messageType: T, data: ToProtocol[T][0], messageId?: string): ToProtocol[T][1];

  dispose(): void;
  checkIsRunning(): boolean;
}
