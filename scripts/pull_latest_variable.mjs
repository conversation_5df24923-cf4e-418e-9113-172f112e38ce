import { writeFileSync } from "fs";
import { dirname, join } from 'node:path';
import { fileURLToPath } from 'node:url';
import _ from 'lodash'

const __dirname = dirname(fileURLToPath(import.meta.url));

async function main() {
  const res = await fetch("https://raw.githubusercontent.com/microsoft/vscode/main/build/lib/stylelint/vscode-known-variables.json").then(res => res.text());
  /**
   * "colors": [
    "--vscode-actionBar-toggledBackground",
    "--vscode-activityBar-activeBackground",
    "--vscode-activityBar-activeBorder",
    "--vscode-activityBar-activeFocusBorder",
   */
  const identifiersText = JSON.parse(res).colors
    .filter(v => v !== /* 特殊变量过滤 这个变量标配了 */'--vscode-quickInput-list-focusBackground')
    .map(cssVar => {
      if (!cssVar.startsWith('--vscode-')) {
        throw new Error(`${cssVar} 不符合规范`);
      }
      const varName = _.camelCase(cssVar.replace('--vscode-', ''));
      return `export const ${varName} = "${cssVar}";`
    }).join('\n\n') + '\n'
  const absPath = join(__dirname, '../', 'webview-ui/src/style/vscode/vscode-known-variables.json')
  const identifiersFilePath = join(__dirname, '../', 'webview-ui/src/style/vscode/vscode-identifiers.ts')
  writeFileSync(absPath, res);
  writeFileSync(identifiersFilePath, identifiersText)
  console.log('已写入');
  console.log(absPath);
  console.log(identifiersFilePath);
}


main();
