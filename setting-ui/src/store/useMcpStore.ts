import { create } from "zustand";
import { immer } from "zustand/middleware/immer";
import { McpServer, McpTool } from "shared/lib/mcp/types";

type State = {
  /** MCP Server列表 */
  servers: McpServer[];
  /** MCP Server全局错误 */
  error?: string;
  /** 工具提示 */
  toolsTooltip?: McpTool;
  /** 当前hover action */
  hoverAction?: string;
};

type Action = {
  setServers: (servers: McpServer[]) => void;
  setError: (error: string) => void;
  setToolsTooltip: (tooltip: McpTool) => void;
  setHoverAction: (action: string) => void;
};

export const useMcpStore = create(
  immer<State & Action>(set => ({
    servers: [],
    error: "",
    setServers: servers => set((state) => {
      state.servers = servers;
    }),
    setError: (error) => {
      set((state) => {
        state.error = error;
      });
    },
    setToolsTooltip: (tooltip) => {
      set((state) => {
        state.toolsTooltip = tooltip;
      });
    },
    setHoverAction: (action) => {
      set((state) => {
        state.hoverAction = action;
      });
    },
  })),
);
