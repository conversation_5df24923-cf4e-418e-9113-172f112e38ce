import { SettingBlock } from "../components/SettingBlock";
import { PageTitle } from "../components/PageTitle";
import { SettingTitle } from "../components/SettingTitle";
import { SettingDesc } from "../components/SettingDesc";
import { VscodeButton } from "@vscode-elements/react-elements";
import { kwaiPilotBridgeAPI } from "@/bridge";

export const Rules = () => {
  const openUserRule = () => {
    kwaiPilotBridgeAPI.extensionRules.$openUserRule();
  };
  const openProjectRules = () => {
    kwaiPilotBridgeAPI.extensionRules.$openProjectRules();
  };
  return (
    <>
      <PageTitle title="规则配置" desc="配置可重复使用的、有范围的规则来控制模型的输出方式。" />
      <SettingBlock className="flex items-center justify-between">
        <div>
          <SettingTitle>个人规则</SettingTitle>
          <SettingDesc>
            在此文件中配置用户习惯后，Kwaipilot在问答模式及智能体模式的所有对话场景中均遵循设定规则，且跨项目切换时持续生效。
          </SettingDesc>
        </div>
        <VscodeButton className="flex h-[28px]" secondary onClick={openUserRule}>打开</VscodeButton>
      </SettingBlock>
      <SettingBlock className="flex items-center justify-between">
        <div>
          <SettingTitle>项目规则</SettingTitle>
          <SettingDesc>
            配置项目内使用的规则，在当前项目的问答模式与智能体模式的会话中生效，可在当前工作区的.kwaipilot/rules文件夹查看当前项目内的所有规则。
          </SettingDesc>
        </div>
        <VscodeButton className="flex h-[28px]" secondary onClick={openProjectRules}>添加</VscodeButton>
      </SettingBlock>
    </>
  );
};
