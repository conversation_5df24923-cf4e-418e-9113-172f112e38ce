import { kwai<PERSON>ilotBridgeAPI } from "@/bridge";
import { InlineLink } from "../components/InlineLink";
import { VscodeTextfield, VscodeButton } from "@vscode-elements/react-elements";
import { useEffect, useMemo, useState } from "react";
import { Icon } from "@iconify/react";
import { SettingTitle } from "../components/SettingTitle";
import { SettingDesc } from "../components/SettingDesc";
import { PageTitle } from "../components/PageTitle";
import { SettingBlock } from "../components/SettingBlock";

import { MAX_INDEX_SPACE_MIN, DEFAULT_INDEX_SPACE } from "shared/lib/const/index";

export const FileIndex = () => {
  const [progress, setProgress] = useState(0);
  const [isStarted, setIsStarted] = useState(false);
  const buildFinish = useMemo(() => progress >= 1, [progress]);
  const [message, setMessage] = useState("");
  const [maxIndexSpace, setMaxIndexSpace] = useState<number>(DEFAULT_INDEX_SPACE);
  const [paused, setPaused] = useState(false);
  const [isRepo, setIsRepo] = useState(true);
  // progress保留到小数点后两位
  const showProgress = useMemo(() => (progress * 100).toFixed(0), [progress]);
  const [lastBuildTime, setLastBuildTime] = useState("");

  const startBuildIndex = async () => {
    if (!isRepo) {
      return;
    }
    await kwaiPilotBridgeAPI.extensionIndexFile.$startBuildIndex();
  };
  const deleteIndex = async () => {
    await kwaiPilotBridgeAPI.extensionIndexFile.$deleteIndex();
  };
  const stopIndexBuild = async () => {
    await kwaiPilotBridgeAPI.extensionIndexFile.$stopIndex();
  };

  // 只负责获取和设置最大索引空间大小
  useEffect(() => {
    const fetchMaxSpaceSize = async () => {
      const value = await kwaiPilotBridgeAPI.extensionIndexFile.$getMaxSpaceSize();
      setMaxIndexSpace(value);
    };

    fetchMaxSpaceSize();
  }, []);

  useEffect(() => {
    const sus = kwaiPilotBridgeAPI.observableAPI.indexState().subscribe((state) => {
      setProgress(state.indexingProgress);
      setIsStarted(state.indexing);
      setLastBuildTime(state.lastBuildTime);
      setMessage(state.indexingMessage);
      setPaused(state.pauseIndexManual);
    });

    return () => {
      sus.unsubscribe();
    };
  }, []);

  useEffect(() => {
    const fetchIsRepo = async () => {
      const value = await kwaiPilotBridgeAPI.extensionIndexFile.$getIsRepo();
      setIsRepo(value);
    };

    fetchIsRepo();
  }, []);

  const handleMaxIndexSpaceChange = (e: Event) => {
    const value = Number((e.target as HTMLInputElement).value);
    if (value < MAX_INDEX_SPACE_MIN || !value) {
      setMaxIndexSpace(maxIndexSpace);
      return;
    }
    setMaxIndexSpace(value);
    kwaiPilotBridgeAPI.extensionIndexFile.$setMaxSpaceSize(value);
  };

  return (
    <>
      <PageTitle title="代码索引管理" />
      <SettingBlock>
        <div className="flex justify-between">
          <div>
            <SettingTitle>手动构建索引</SettingTitle>
            <SettingDesc>
              构建仓库代码的全局索引，当发起智能体会话时将自动检索问题相关上下文，提升代码问答准确性
            </SettingDesc>
          </div>
          <div className="flex gap-3 h-[34px]">
            {!isStarted && !buildFinish && (
              <VscodeButton
                disabled={!isRepo}
                className="flex"
                onClick={
                  startBuildIndex
                }
              >
                开始构建
              </VscodeButton>
            )}
            { isStarted && !buildFinish
            && (
              <VscodeButton className="flex" onClick={stopIndexBuild}>
                <Icon icon="codicon:close"></Icon>
                取消构建
              </VscodeButton>
            )}
            {buildFinish && (
              <VscodeButton className="flex" onClick={startBuildIndex}>
                <Icon icon="codicon:debug-restart"></Icon>
                重新构建
              </VscodeButton>
            )}
            {buildFinish && (
              <VscodeButton secondary className="flex" onClick={deleteIndex}>
                <Icon icon="material-symbols:delete-outline"></Icon>
                删除索引
              </VscodeButton>
            )}
            {/* {buildFinish && <VscodeButton secondary className="flex">查看索引</VscodeButton>} */}
          </div>
        </div>
        <div className="pt-4 pb-2">
          <div className="w-full h-1.5 bg-[var(--vscode-tab-inactiveBackground)] rounded-[3px]">
            <div className={`w-full h-full  rounded-[3px] ${buildFinish ? "bg-[#00C2A5ff]" : "bg-[var(--vscode-progressBar-background)]"}`} style={{ width: `${showProgress}%` }}></div>
          </div>
        </div>
        <div className="flex justify-between">
          <SettingDesc>
            {!isRepo
              ? (
                  <>
                    <Icon icon="ix:error-filled" className="text-[#E35151]" />
                    不是git仓库
                  </>
                )
              : paused
                ? "已暂停索引"
                : message
                  ? message
                  : buildFinish
                    ? (
                        <>
                          <Icon icon="mdi:success-circle" className="text-[#00C2A5ff]"></Icon>
                          {`构建成功 ${lastBuildTime}`}
                        </>
                      )
                    : isStarted ? "构建索引中..." : "当前未构建索引"}
          </SettingDesc>
          <div>
            {showProgress}
            %
          </div>
        </div>
      </SettingBlock>
      <SettingBlock>
        <div>
          <SettingTitle>忽略文件目录</SettingTitle>
          <SettingDesc>
            配置构建代码索引时忽略的文件目录
            <InlineLink onClick={() => {
              kwaiPilotBridgeAPI.extensionIndexFile.$openIndexIgnore();
            }}
            >
              {" "}
              前往配置
            </InlineLink>
          </SettingDesc>
        </div>
      </SettingBlock>
      <SettingBlock>
        <div className="flex flex-col gap-2">
          <SettingTitle>最大索引空间大小（单位：GB）</SettingTitle>
          <VscodeTextfield
            className="border-[var(--vscode-settings-textInputBorder)] focus:border-[var(--vscode-focusBorder)] border-[1px]"
            type="number"
            onInput={handleMaxIndexSpaceChange}
            value={String(maxIndexSpace)}
            min={MAX_INDEX_SPACE_MIN}
          />
        </div>
      </SettingBlock>
    </>
  );
};
