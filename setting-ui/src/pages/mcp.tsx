import { useEffect, useMemo } from "react";
import { useAsync, useAsyncFn } from "react-use";
import { Icon } from "@iconify/react";
import { MCPServerCard } from "../components/MCPServerCard";
import { VscodeButton } from "@vscode-elements/react-elements";
import { kwaiPilotBridgeAPI } from "@/bridge";
import { Tooltip } from "react-tooltip";
import { useMcpStore } from "../store/useMcpStore";
import { ReportOpt } from "@shared/types/logger";

const today = new Date().toISOString().slice(0, 10);
const storageKey = "mcp_user_count_reported_date";
const lastReported = localStorage.getItem(storageKey);

/** MCP 管理页面 */
export const MCP = () => {
  const servers = useMcpStore(state => state.servers);
  const setServers = useMcpStore(state => state.setServers);
  const error = useMcpStore(state => state.error);
  const setError = useMcpStore(state => state.setError);
  const toolsTooltip = useMcpStore(state => state.toolsTooltip);
  const hoverAction = useMcpStore(state => state.hoverAction);

  const mcpServersCount = useMemo(() => servers.length, [servers]);
  const toolsCount = useMemo(() => servers.filter(server => !server.disabled).reduce((acc, server) => acc + (server.tools?.length ?? 0), 0), [servers]);

  const handleMcpMarket = () => {
    const param: ReportOpt<"mcp_jump"> = {
      key: "mcp_jump",
      type: "mcp_setting_hub",
    };
    kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction(param);
    kwaiPilotBridgeAPI.openUrl("https://mcp-hub.corp.kuaishou.com");
  };

  const reportMcpUserCount = (serversName: string[]) => {
    if (serversName.length === 0 || lastReported === today) return;
    // 每天埋一次MCP的配置人数与工具数
    const param: ReportOpt<"mcp_user_count"> = {
      key: "mcp_user_count",
      type: "mcp_user_conf",
      content: JSON.stringify(serversName.join(",")),
    };
    kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction(param);
    localStorage.setItem(storageKey, today);
  };

  useAsync(async () => {
    const res = await kwaiPilotBridgeAPI.extensionMCP.$getAllMcpServers();
    if (res.code !== 0 || res.status === "failed" || res?.data?.isError) {
      setError(res?.message ?? "获取MCP服务器列表失败");
      return;
    }
    setError("");
    reportMcpUserCount(res?.data?.mcpServers?.filter(server => !server.disabled).map(server => server.name) ?? []);
    setServers(res?.data?.mcpServers ?? []);
  }, [setError, setServers]);

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, getSettingsPath] = useAsyncFn(async () => {
    const res = await kwaiPilotBridgeAPI.extensionMCP.$getSettingsPath();
    if (res.code !== 0 || res.status === "failed") {
      return;
    }
    else {
      kwaiPilotBridgeAPI.editor.openFileToEditor(res.data || "");
    }
  }, []);

  useEffect(() => {
    const sus = kwaiPilotBridgeAPI.observableAPI.mcpServers().subscribe((state) => {
      if (state.code !== 0 || state.isError) {
        setError(state?.message ?? "获取MCP服务器列表失败");
        return;
      }
      setError("");
      setServers(state.mcpServers);
    });
    return () => {
      sus.unsubscribe();
    };
  }, [setError, setServers]);

  const renderServersPlaceholder = useMemo(() => {
    if (error) {
      return (
        <div className="py-7 text-[var(--vscode-errorForeground)] flex items-center justify-center border border-[var(--vscode-widget-border)] rounded-md bg-[var(--vscode-listFilterWidget-background)] break-all px-4">
          {error}
        </div>
      );
    }
    if (mcpServersCount === 0) {
      return (
        <div className="py-7 flex items-center justify-center border border-[var(--vscode-widget-border)] rounded-md bg-[var(--vscode-listFilterWidget-background)]">
          暂未添加MCP Servers，请添加
        </div>
      );
    }
    return null;
  }, [error, mcpServersCount]);

  const renderMcpServers = useMemo(() => {
    return servers.map(server => (
      <MCPServerCard
        key={server.name}
        {...server}
        onEdit={() => {
          const param: ReportOpt<"mcp_jump"> = {
            key: "mcp_jump",
            type: "mcp_setting_jsonconf",
            content: server.name,
          };
          kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction(param);
          getSettingsPath();
        }}
      />
    ));
  }, [getSettingsPath, servers]);

  const renderToolsTooltip = useMemo(() => {
    const properties = (toolsTooltip?.inputSchema as unknown as { properties: Record<string, any> })?.properties || {};
    const required = (toolsTooltip?.inputSchema as unknown as { required: string[] })?.required || [];
    return (
      <Tooltip id="mcp-server-tools-tooltip" style={{ backgroundColor: "#3C3C3C", opacity: 1, padding: 0, borderRadius: 0 }} place="bottom-start">
        <div className="max-w-[411px] max-h-[246px] overflow-auto text-[12px]">
          <div className="p-1 border-b border-[var(--vscode-widget-border)]">
            {toolsTooltip?.description}
          </div>
          {
            Object.entries(properties).length > 0 && (
              <div className="p-1">
                <div className="mb-1">参数</div>
                <div>
                  {Object.entries(properties).map(([key, value]) => (
                    <div className="flex mt-1" key={key}>
                      <div className="mr-1 font-bold">
                        {
                          required?.includes(key) && (
                            <span className="text-[#C74E39ff] mr-1">*</span>
                          )
                        }
                        {`${key}:`}
                      </div>
                      <div>{(value as any)?.description || "--"}</div>
                    </div>
                  ))}
                </div>
              </div>
            )
          }
        </div>
      </Tooltip>
    );
  }, [toolsTooltip]);

  const renderActionTooltip = useMemo(() => {
    return (
      <Tooltip id="mcp-server-action-tooltip" style={{ backgroundColor: "#3C3C3C", opacity: 1, padding: "4px 8px", borderRadius: 0 }}>
        <div className="text-[12px]">
          {hoverAction}
        </div>
      </Tooltip>
    );
  }, [hoverAction]);

  return (
    <div className="max-w-[900px]">
      <div className="h-[42px] flex items-center pl-[15px] justify-between">
        <div className="text-[22px] mr-6 font-medium flex-shrink-0">
          MCP 管理
        </div>
        {
          toolsCount > 40 && (
            <div className="text-[13px] text-[var(--vscode-descriptionForeground)] flex flex-1 justify-end items-center gap-1">
              <Icon icon="codicon:warning" className="inline-block text-[#FFBB26]" />
              <div className="line-clamp-1">
                {`已启用
            ${toolsCount}
            个工具，超40个工具会降低性能且部分模型不支持，系统将自动裁剪多余工具。`}
              </div>
            </div>
          )
        }
      </div>

      <div className="px-[15px] pt-3 pb-6">
        <div className="flex items-center justify-between mb-2">
          <div>
            <div className="text-[13px] font-medium leading-[18px] mb-1">
              MCP Servers (
              {mcpServersCount}
              )
            </div>
            <div className="text-[12px] text-[var(--vscode-descriptionForeground)] mr-6">
              模型上下文协议 (Model Context Protocol, MCP) 是一种为 Kwaipilot 助理提供工具和功能来扩展助理的能力，可以手动或从快手 MCP 市场添加 MCP Servers。
            </div>
          </div>
          <div className="flex gap-3">
            <VscodeButton
              className="h-[28px] flex items-center"
              onClick={() => {
                const param: ReportOpt<"mcp_jump"> = {
                  key: "mcp_jump",
                  type: "mcp_setting_jsonconf",
                };
                kwaiPilotBridgeAPI.extensionWeblogger.$reportUserAction(param);
                getSettingsPath();
              }}
            >
              手动配置
            </VscodeButton>
            <VscodeButton
              className="h-[28px] flex items-center"
              secondary
              onClick={handleMcpMarket}
            >
              MCP市场
              <Icon icon="prime:external-link" className="size-[16px] ml-1" />
            </VscodeButton>
          </div>
        </div>
        {renderServersPlaceholder || renderMcpServers}
        {renderToolsTooltip}
        {renderActionTooltip}
      </div>
    </div>
  );
};
